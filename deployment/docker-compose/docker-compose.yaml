version: '3'
services:
  mongodb:
    build:
      context: ./mongodb
      dockerfile: Dockerfile
    hostname: mongodb
    container_name: mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=vctrade_r00t_adm1n_user
      - MONGO_INITDB_ROOT_PASSWORD=vc1rade_r00t_adm1n_passw0rd
      - PUID=1000
      - PGID=1000
    command: ["-f", "/etc/mongod.conf"]
    restart: on-failure
    ports:
      - "27017:27017"
    volumes:
      - ./mongodb_data_container:/data/db
      - ./mongodb/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - docker-internal-network
  finchat-service:
    container_name: finchat-service
    build: ../../.
    ports:
      - "8087:8087"
    environment:
      - SPRING_DATA_MONGODB_CONNECTIONSTRING=****************************************************************************************************
      - SPRING_DATA_MONGODB_CONNECTION_STRING=****************************************************************************************************
      - SPRING_RABBITMQ_HOST=rabbitmq
    networks:
      - docker-internal-network
    depends_on:
      - mongodb
volumes:
  mongodb:
    driver: local
networks:
  docker-internal-network:
    driver: bridge