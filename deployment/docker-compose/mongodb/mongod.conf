# Storage configuration
storage:
  dbPath: /data/db
  journal:
    enabled: true

# Log configuration
systemLog:
  destination: file
  path: /var/log/mongodb/mongod.log
  logAppend: true

# Network interfaces
net:
  port: 27017
  bindIp: 127.0.0.1

# Security settings
security:
  authorization: enabled
  keyFile: /opt/keyfile/mongo-keyfile

# Replica Set configuration
replication:
  replSetName: rs0

# Operation Profiling
operationProfiling:
  mode: slowOp
  slowOpThresholdMs: 100

# Other options and settings can be added based on your needs.