package de.vctrade.finchat.model.entity;

import de.vctrade.mongodb.service.data.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

@Document("external_contexts")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ExternalContextEntity extends BaseEntity {

    private SourceType sourceType;

    private String context;

    private ExternalContextState state;

}
