package de.vctrade.finchat.model.entity;

import de.vctrade.finchat.domain.generated.dto.Role;
import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.UUID;

@Document("chat_messages")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ChatMessageEntity extends BaseEntity {

    @NotNull
    private UUID conversationID;

    private String content;

    private ChatSettings chatSettings;

    private Role role;

}
