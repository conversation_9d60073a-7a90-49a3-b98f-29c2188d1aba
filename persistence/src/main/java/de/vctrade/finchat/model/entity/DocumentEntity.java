package de.vctrade.finchat.model.entity;

import de.vctrade.finchat.ingest.domain.model.document.ContentMetadata;
import de.vctrade.finchat.ingest.domain.model.document.ContentNodeData;
import de.vctrade.finchat.ingest.domain.model.document.DocumentMetadata;
import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.UUID;

@Document("documents")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class DocumentEntity extends BaseEntity {

    @Id
    private UUID id;

    @NotNull
    private UUID owner;

    @NotNull
    private DocumentMetadata document;

    @NotNull
    private ContentMetadata content;

    @NotNull
    private ContentNodeData node;

}
