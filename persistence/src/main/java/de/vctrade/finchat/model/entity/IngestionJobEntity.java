package de.vctrade.finchat.model.entity;

import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.UUID;

@Document("ingestion_jobs")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class IngestionJobEntity extends BaseEntity {

    @NotNull
    private UUID documentId;

    @NotNull
    private IngestionJobStatus status;

    @Size(max = 1000)
    private String errorMessage;

    private Long startedAtEpochMillis;

    private Long updatedAtEpochMillis;

    private Long completedAtEpochMillis;

    @Size(max = 255)
    private String jobType;

    private Integer retryCount;

    public enum IngestionJobStatus {
        QUEUED, RUNNING, COMPLETED, FAILED, CANCELLED, RETRYING
    }
}
