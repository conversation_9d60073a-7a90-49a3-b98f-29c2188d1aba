package de.vctrade.finchat.model.entity;

import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.UUID;

@Document("conversations")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ConversationEntity extends BaseEntity {

    private List<UUID> externalContextID;

    private UUID referenceID;

    @Size(max = 120)
    private String summary;

    private ReferenceType referenceType;

}
