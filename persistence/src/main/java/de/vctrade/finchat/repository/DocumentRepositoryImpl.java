package de.vctrade.finchat.repository;

import de.vctrade.finchat.ingest.domain.model.document.CreateDocumentData;
import de.vctrade.finchat.ingest.domain.model.document.DocumentAggregate;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.repository.DocumentRepository;
import de.vctrade.finchat.mapper.DocumentEntityMapper;
import de.vctrade.finchat.model.entity.DocumentEntity;
import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class MongoDocumentRepositoryImpl implements DocumentRepository {

    private final ReactiveMongoTemplate mongoTemplate;

    private final DocumentEntityMapper mapper;

    public MongoDocumentRepositoryImpl(
            ReactiveMongoTemplate mongoTemplate,
            DocumentEntityMapper mapper
    ) {
        this.mongoTemplate = mongoTemplate;
        this.mapper = mapper;
    }

    public @NotNull DocumentAggregate create(
            final @NotNull
            CreateDocumentData data
    ) {
        final var document = DocumentAggregate.builder()
                .id(UUID.randomUUID())
                .documentMetadata(data.documentMetadata())
                .contentMetadata(data.contentMetadata())
                .indexingStage(IndexingStage.PENDING)
                .build();
        mongoTemplate.
        return mapper.convert(super.save(mapper.convert(document)));
    }

    @Override
    public Mono<DocumentAggregate> create(CreateDocumentData document) {
        return null;
    }

    @Override
    public @NotNull List<DocumentResponse> fetchAll() {
        return super.findAll().stream()
                .map(mapper::convert)
                .toList();
    }

    @Override
    public @Nullable DocumentResponse fetchById(@NotNull final UUID id) {
        var documentEntity = super.findById(id);

        return documentEntity != null ? mapper.convert(documentEntity) : null;
    }

    @Override
    public @Nullable DocumentResponse fetchByExternalId(@NotNull final String externalId) {
        final Criteria criteria = Criteria.where(DocumentEntity.Fields.externalId).is(externalId);

        this.addFilterByAuthentication(criteria);

        final DocumentEntity entity = this.getMongoTemplate().findOne(Query.query(criteria), DocumentEntity.class);
        return entity != null ? mapper.convert(entity) : null;
    }

    @Override
    public @NotNull List<DocumentResponse> fetchByCollectionId(@NotNull final UUID collectionId) {
        final Criteria criteria = Criteria.where(DocumentEntity.Fields.collectionId).is(collectionId);
        this.addFilterByAuthentication(criteria);

        return this.getMongoTemplate()
                .find(Query.query(criteria), DocumentEntity.class).stream()
                .map(mapper::convert)
                .toList();
    }

    @Override
    public @NotNull List<DocumentResponse> fetchByRootId(@NotNull final UUID rootId) {
        final Criteria criteria = Criteria.where(DocumentEntity.Fields.rootId).is(rootId);
        this.addFilterByAuthentication(criteria);

        return this.getMongoTemplate()
                .find(Query.query(criteria), DocumentEntity.class).stream()
                .map(mapper::convert)
                .toList();
    }

    @Override
    public Flux<DocumentAggregate> fetchByIndexingStatus(IndexingStage status) {
        return null;
    }

    @Override
    public Mono<Void> updateIndexingStage(UUID id, IndexingStage indexingStage, @org.jetbrains.annotations.Nullable String errorMessage) {
        return null;
    }

    @Override
    public @NotNull List<DocumentResponse> fetchByIndexingStatus(@NotNull final DocumentIndexingStatus status) {
        final Criteria criteria = Criteria.where(DocumentEntity.Fields.indexingStatus).is(status);
        this.addFilterByAuthentication(criteria);

        return this.getMongoTemplate()
                .find(Query.query(criteria), DocumentEntity.class).stream()
                .map(mapper::convert)
                .toList();
    }

    @Override
    public @NotNull DocumentResponse updateIndexingStatus(
            @NotNull final UUID id,
            @NotNull final DocumentIndexingStatus status,
            @Nullable final String errorMessage
    ) {
        final Criteria criteria = Criteria.where(BaseEntity.Fields.id).is(id);
        this.addFilterByAuthentication(criteria);

        final Update update = new Update()
                .set(DocumentEntity.Fields.indexingStatus, status);

        if (errorMessage != null) {
            update.set(DocumentEntity.Fields.indexingErrorMessage, errorMessage);
        }

        this.getMongoTemplate().updateFirst(Query.query(criteria), update, DocumentEntity.class);

        // Fetch and return updated entity
        final DocumentEntity updatedEntity = super.findById(id);
        return mapper.convert(updatedEntity);
    }
}
