package de.vctrade.finchat.repository;

import de.vctrade.finchat.api.service.api.ConversationRepository;
import de.vctrade.finchat.domain.generated.dto.ConversationResponse;
import de.vctrade.finchat.domain.generated.dto.CreateConversationRequest;
import de.vctrade.finchat.mapper.ConversationMapper;
import de.vctrade.finchat.model.entity.ConversationEntity;
import de.vctrade.mongodb.service.AbstractBaseCRUDService;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ConversationRepositoryImpl extends AbstractBaseCRUDService<ConversationEntity> implements ConversationRepository {

    private final ConversationMapper mapper;

    protected ConversationRepositoryImpl(MongoTemplate mongoTemplate, final @NotNull ConversationMapper mapper) {
        super(mongoTemplate, false);
        this.mapper = mapper;
    }

    @Override
    public @NotNull ConversationResponse save(final @NotNull CreateConversationRequest conversation) {
        return mapper.convert(super.save(mapper.convert(conversation)));
    }

    @Override
    public @NotNull List<ConversationResponse> fetchAll(){
       return super.findAll().stream().map(
               mapper::convert
        ).toList();
    }

    @Override
    public @Nullable ConversationResponse fetchID(@NotNull final UUID id ){
        var conversationEntity =  super.findById(id);

        return conversationEntity != null ? mapper.convert(conversationEntity) : null;
    }

    @Override
    public @NotNull List<ConversationResponse> fetchByReferenceId(final @NotNull UUID referenceId){

        final Criteria criteria = Criteria.where(ConversationEntity.Fields.referenceID).is(referenceId);
        this.addFilterByAuthentication(criteria);
        return this.getMongoTemplate().find(Query.query(criteria), ConversationEntity.class).stream().map(
                mapper::convert
        ).toList();

    }
}
