package de.vctrade.finchat.repository;

import de.vctrade.finchat.ingest.domain.model.ingest.CreateIngestionJobData;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.ingest.domain.repository.IngestionJobRepository;
import de.vctrade.finchat.mapper.IngestionJobMapper;
import de.vctrade.finchat.model.entity.IngestionJobEntity;
import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

@Slf4j
@Service
public class IngestionJobRepositoryImpl implements IngestionJobRepository {

    private final ReactiveMongoTemplate mongoTemplate;
    private final IngestionJobMapper mapper;

    protected IngestionJobRepositoryImpl(
            final ReactiveMongoTemplate mongoTemplate,
            final IngestionJobMapper mapper
    ) {
        this.mongoTemplate = mongoTemplate;
        this.mapper = mapper;
    }

    @Override
    public Mono<IngestionJob> create(final @NotNull CreateIngestionJobData ingestionJob) {
        var newEntity = mapper.convert(ingestionJob);

        return mongoTemplate.insert(newEntity)
                .map(mapper::convert);
    }

    @Override
    public Flux<IngestionJob> fetchAll() {
        return mongoTemplate.findAll(IngestionJobEntity.class)
                .map(mapper::convert);
    }

    @Override
    public Mono<IngestionJob> fetchById(@NotNull final UUID id) {
        return mongoTemplate.findById(id, IngestionJobEntity.class)
                .map(mapper::convert);
    }

    @Override
    public Flux<IngestionJob> fetchByStatus(
            final IndexingStage stage
    ) {
        var status = mapStage(stage);
        final Criteria criteria = Criteria
                .where(IngestionJobEntity.Fields.status).is(status);

        return this.mongoTemplate
                .find(Query.query(criteria), IngestionJobEntity.class)
                .map(mapper::convert);
    }

    @Override
    public Flux<IngestionJob> fetchByDocumentId(@NotNull final UUID documentId) {
        final Criteria criteria = Criteria
                .where(IngestionJobEntity.Fields.documentId).is(documentId);

        return this.mongoTemplate
                .find(Query.query(criteria), IngestionJobEntity.class)
                .map(mapper::convert);
    }

    @Override
    public @NotNull Mono<Void> updateStage(
            @NotNull final UUID id,
            @NotNull final IndexingStage stage,
            @Nullable final String errorMessage
    ) {
        var status = mapStage(stage);
        final Criteria criteria = Criteria.where(BaseEntity.Fields.id).is(id);

        final Update update = new Update()
                .set(IngestionJobEntity.Fields.status, status);

        if (errorMessage != null) {
            update.set(IngestionJobEntity.Fields.errorMessage, errorMessage);
        }

        // Set completion timestamp if job is completed or failed
        switch (status) {
            case RUNNING -> {
                update.set(IngestionJobEntity.Fields.startedAtEpochMillis, System.currentTimeMillis());
            }
            case COMPLETED, FAILED -> {
                update.set(IngestionJobEntity.Fields.completedAtEpochMillis, System.currentTimeMillis());
            }
            default -> {}
        }

        return this.getMongoTemplate()
                .updateFirst(Query.query(criteria), update, IngestionJobEntity.class)
                .then();
    }

    private static @NotNull IngestionJobEntity.IngestionJobStatus mapStage(IndexingStage stage) {
        return switch (stage) {
            case PENDING -> IngestionJobEntity.IngestionJobStatus.QUEUED;
            case EXTRACTING -> IngestionJobEntity.IngestionJobStatus.RUNNING;
            case COMPLETED -> IngestionJobEntity.IngestionJobStatus.COMPLETED;
            case FAILED -> IngestionJobEntity.IngestionJobStatus.FAILED;
            case CANCELLED -> IngestionJobEntity.IngestionJobStatus.CANCELLED;
        };
    }

    private ReactiveMongoTemplate getMongoTemplate() {
        return this.mongoTemplate;
    }

}
