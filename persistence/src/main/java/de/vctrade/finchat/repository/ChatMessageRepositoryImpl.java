package de.vctrade.finchat.repository;

import de.vctrade.finchat.api.service.api.ChatMessageRepository;
import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import de.vctrade.finchat.mapper.ChatMessageMapper;
import de.vctrade.finchat.model.entity.ChatMessageEntity;
import de.vctrade.mongodb.service.AbstractBaseCRUDService;
import de.vctrade.mongodb.service.data.BaseEntity;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;


@Slf4j
@Service
public class ChatMessageRepositoryImpl extends AbstractBaseCRUDService<ChatMessageEntity> implements ChatMessageRepository {

    private final ChatMessageMapper mapper;

    protected ChatMessageRepositoryImpl(MongoTemplate mongoTemplate, final @NotNull ChatMessageMapper mapper) {
        super(mongoTemplate, false);
        this.mapper = mapper;
    }

    @Override
    public @NotNull MessageResponse save(final @NotNull UUID conversationId,  final @NotNull MessageRequest chatMessage) {
        return mapper.convert(super.save(mapper.convert(conversationId, chatMessage)));
    }

    @Override
    public MessageResponse save(final @NotNull UUID userId, final @NotNull UUID conversationId, final @NotNull MessageRequest chatMessage){

        final var entity = mapper.convert(conversationId, chatMessage);
        if(userId != null) {
            entity.setCreatedByUser(userId);
            entity.setParticipantId(List.of(userId));
        }
        return mapper.convert(super.save(entity));
    }



    @Override
    public @NotNull List<MessageResponse> fetchAll() {
        return super.findAll().stream().map(
                mapper::convert
        ).toList();
    }

    @Override
    public @NotNull List<MessageResponse> fetchByConversationId(@NotNull UUID conversationId) {
        final Criteria criteria = Criteria.where(ChatMessageEntity.Fields.conversationID).is(conversationId);
        this.addFilterByAuthentication(criteria);

        final Query query = Query.query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, BaseEntity.Fields.createdAtEpochMillis));

        return this.getMongoTemplate().find(query, ChatMessageEntity.class).stream().map(
                mapper::convert
        ).toList();
    }

    @Nullable
    @Override
    public MessageResponse fetchID(@NotNull UUID id) {
        var conversationEntity =  super.findById(id);

        return conversationEntity != null ? mapper.convert(conversationEntity) : null;
    }
}
