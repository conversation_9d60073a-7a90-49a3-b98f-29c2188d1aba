package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.ConversationResponse;
import de.vctrade.finchat.domain.generated.dto.CreateConversationRequest;
import de.vctrade.finchat.model.entity.ConversationEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ConversationMapper {

    @Mapping(source = "sourceIds", target = "externalContextID")
    @Mapping(source = "correlationId", target = "referenceID")
    @Mapping(target = "referenceType", constant = "DEAL")
    ConversationEntity convert(CreateConversationRequest requestDto);


    @Mapping(source = "externalContextID", target = "sourceIds")
    @Mapping(source = "referenceID", target = "correlationId")
    @Mapping(source = "createdAtEpochMillis", target="createdAt")
    ConversationResponse convert(ConversationEntity entity);
}
