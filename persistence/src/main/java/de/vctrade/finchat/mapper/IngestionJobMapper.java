package de.vctrade.finchat.mapper;

import de.vctrade.finchat.ingest.adapter.model.CreateIngestionJobRequest;
import de.vctrade.finchat.ingest.domain.model.ingest.CreateIngestionJobData;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.model.entity.IngestionJobEntity;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface IngestionJobMapper {

    @Mapping(target = "status", constant = "QUEUED")
    @Mapping(target = "retryCount", constant = "0")
    IngestionJobEntity convert(CreateIngestionJobRequest requestDto);

    default IngestionJobEntity convert(CreateIngestionJobData data) {
        return IngestionJobEntity.builder()
                .documentId(data.documentId())
                .status(IngestionJobEntity.IngestionJobStatus.QUEUED)
                .build();

    }

    @Mapping(source = "createdAtEpochMillis", target = "createdAt")
    @Mapping(source = "startedAtEpochMillis", target = "startedAt")
    @Mapping(source = "completedAtEpochMillis", target = "completedAt")
    IngestionJob convert(IngestionJobEntity entity);
}
