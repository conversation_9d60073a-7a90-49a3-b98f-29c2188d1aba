package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import de.vctrade.finchat.model.entity.ChatMessageEntity;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface ChatMessageMapper {

    ChatMessageEntity convert(final @NotNull UUID conversationID, final @NotNull MessageRequest requestDto);


    @Mapping(source = "createdAtEpochMillis", target="createdAt")
    MessageResponse convert(ChatMessageEntity entity);
}
