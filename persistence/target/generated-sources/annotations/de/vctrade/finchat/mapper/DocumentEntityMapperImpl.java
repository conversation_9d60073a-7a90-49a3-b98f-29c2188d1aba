package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.CreateDocumentRequest;
import de.vctrade.finchat.domain.generated.dto.DocumentResponse;
import de.vctrade.finchat.domain.model.DocumentIndexingStatus;
import de.vctrade.finchat.model.entity.DocumentEntity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-27T03:23:58+0200",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.6 (Azul Systems, Inc.)"
)
@Component
public class DocumentEntityMapperImpl implements DocumentEntityMapper {

    @Override
    public DocumentEntity convert(CreateDocumentRequest requestDto) {
        if ( requestDto == null ) {
            return null;
        }

        DocumentEntity.DocumentEntityBuilder<?, ?> documentEntity = DocumentEntity.builder();

        documentEntity.externalId( requestDto.getExternalId() );
        documentEntity.contentType( requestDto.getContentType() );
        documentEntity.collectionId( requestDto.getCollectionId() );
        documentEntity.rootId( requestDto.getRootId() );

        documentEntity.indexingStatus( DocumentIndexingStatus.PENDING );

        return documentEntity.build();
    }

    @Override
    public DocumentResponse convert(DocumentEntity entity) {
        if ( entity == null ) {
            return null;
        }

        DocumentResponse documentResponse = new DocumentResponse();

        documentResponse.setCreatedAt( entity.getCreatedAtEpochMillis() );
        documentResponse.setId( entity.getId() );
        documentResponse.setExternalId( entity.getExternalId() );
        documentResponse.setContentType( entity.getContentType() );
        documentResponse.setCollectionId( entity.getCollectionId() );
        documentResponse.setRootId( entity.getRootId() );
        if ( entity.getIndexingStatus() != null ) {
            documentResponse.setIndexingStatus( entity.getIndexingStatus().name() );
        }

        return documentResponse;
    }
}
