package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.ConversationResponse;
import de.vctrade.finchat.domain.generated.dto.CreateConversationRequest;
import de.vctrade.finchat.model.entity.ConversationEntity;
import de.vctrade.finchat.model.entity.ReferenceType;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-27T15:42:06+0200",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.6 (Azul Systems, Inc.)"
)
@Component
public class ConversationMapperImpl implements ConversationMapper {

    @Override
    public ConversationEntity convert(CreateConversationRequest requestDto) {
        if ( requestDto == null ) {
            return null;
        }

        ConversationEntity.ConversationEntityBuilder<?, ?> conversationEntity = ConversationEntity.builder();

        List<UUID> list = requestDto.getSourceIds();
        if ( list != null ) {
            conversationEntity.externalContextID( new ArrayList<UUID>( list ) );
        }
        conversationEntity.referenceID( requestDto.getCorrelationId() );
        conversationEntity.summary( requestDto.getSummary() );

        conversationEntity.referenceType( ReferenceType.DEAL );

        return conversationEntity.build();
    }

    @Override
    public ConversationResponse convert(ConversationEntity entity) {
        if ( entity == null ) {
            return null;
        }

        ConversationResponse.Builder conversationResponse = ConversationResponse.builder();

        List<UUID> list = entity.getExternalContextID();
        if ( list != null ) {
            conversationResponse.sourceIds( new ArrayList<UUID>( list ) );
        }
        conversationResponse.correlationId( entity.getReferenceID() );
        conversationResponse.createdAt( entity.getCreatedAtEpochMillis() );
        conversationResponse.id( entity.getId() );
        conversationResponse.summary( entity.getSummary() );

        return conversationResponse.build();
    }
}
