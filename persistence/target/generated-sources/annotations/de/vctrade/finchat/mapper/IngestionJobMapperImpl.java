package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.CreateIngestionJobRequest;
import de.vctrade.finchat.domain.generated.dto.IngestionJobResponse;
import de.vctrade.finchat.model.entity.IngestionJobEntity;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-27T03:23:58+0200",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.6 (Azul Systems, Inc.)"
)
@Component
public class IngestionJobMapperImpl implements IngestionJobMapper {

    @Override
    public IngestionJobEntity convert(CreateIngestionJobRequest requestDto) {
        if ( requestDto == null ) {
            return null;
        }

        IngestionJobEntity.IngestionJobEntityBuilder<?, ?> ingestionJobEntity = IngestionJobEntity.builder();

        ingestionJobEntity.documentId( requestDto.getDocumentId() );

        ingestionJobEntity.status( IngestionJobEntity.IngestionJobStatus.QUEUED );
        ingestionJobEntity.retryCount( 0 );

        return ingestionJobEntity.build();
    }

    @Override
    public IngestionJobResponse convert(IngestionJobEntity entity) {
        if ( entity == null ) {
            return null;
        }

        IngestionJobResponse ingestionJobResponse = new IngestionJobResponse();

        ingestionJobResponse.setCreatedAt( entity.getCreatedAtEpochMillis() );
        ingestionJobResponse.setStartedAt( entity.getStartedAtEpochMillis() );
        ingestionJobResponse.setCompletedAt( entity.getCompletedAtEpochMillis() );
        ingestionJobResponse.setId( entity.getId() );
        ingestionJobResponse.setDocumentId( entity.getDocumentId() );
        if ( entity.getStatus() != null ) {
            ingestionJobResponse.setStatus( entity.getStatus().name() );
        }
        ingestionJobResponse.setProgress( entity.getProgress() );
        ingestionJobResponse.setErrorMessage( entity.getErrorMessage() );
        ingestionJobResponse.setRetryCount( entity.getRetryCount() );

        return ingestionJobResponse;
    }
}
