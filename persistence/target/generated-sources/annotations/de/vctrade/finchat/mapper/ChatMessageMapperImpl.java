package de.vctrade.finchat.mapper;

import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import de.vctrade.finchat.model.entity.ChatMessageEntity;
import de.vctrade.finchat.model.entity.ChatSettings;
import java.util.UUID;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-27T15:42:07+0200",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.6 (Azul Systems, Inc.)"
)
@Component
public class ChatMessageMapperImpl implements ChatMessageMapper {

    @Override
    public ChatMessageEntity convert(UUID conversationID, MessageRequest requestDto) {
        if ( conversationID == null && requestDto == null ) {
            return null;
        }

        ChatMessageEntity.ChatMessageEntityBuilder<?, ?> chatMessageEntity = ChatMessageEntity.builder();

        if ( requestDto != null ) {
            chatMessageEntity.content( requestDto.getContent() );
            chatMessageEntity.chatSettings( chatSettingsToChatSettings( requestDto.getChatSettings() ) );
            chatMessageEntity.role( requestDto.getRole() );
        }
        chatMessageEntity.conversationID( conversationID );

        return chatMessageEntity.build();
    }

    @Override
    public MessageResponse convert(ChatMessageEntity entity) {
        if ( entity == null ) {
            return null;
        }

        MessageResponse.Builder messageResponse = MessageResponse.builder();

        messageResponse.createdAt( entity.getCreatedAtEpochMillis() );
        messageResponse.id( entity.getId() );
        messageResponse.role( entity.getRole() );
        messageResponse.content( entity.getContent() );

        return messageResponse.build();
    }

    protected ChatSettings chatSettingsToChatSettings(de.vctrade.finchat.domain.generated.dto.ChatSettings chatSettings) {
        if ( chatSettings == null ) {
            return null;
        }

        ChatSettings.ChatSettingsBuilder<?, ?> chatSettings1 = ChatSettings.builder();

        if ( chatSettings.getUseAdvancedModel() != null ) {
            chatSettings1.useAdvancedModel( chatSettings.getUseAdvancedModel() );
        }

        return chatSettings1.build();
    }
}
