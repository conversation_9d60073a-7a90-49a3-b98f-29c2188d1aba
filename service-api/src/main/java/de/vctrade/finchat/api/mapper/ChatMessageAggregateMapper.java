package de.vctrade.finchat.api.mapper;

import de.vctrade.finchat.api.aggregate.ChatMessageAggregate;
import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.domain.generated.dto.ChatSettings;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import jakarta.validation.constraints.NotNull;
import org.mapstruct.Mapper;

import java.util.UUID;

@Mapper(componentModel = "spring")
public interface ChatMessageAggregateMapper {

    @NotNull
    ChatMessageAggregate convert(final @NotNull MessageResponse message);

    @NotNull
    MessageAggregate convertToAggregate(final UUID userId, final UUID conversationId, final ChatSettings chatSettings, final @NotNull MessageResponse message);
}
