package de.vctrade.finchat.api.aggregate;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

import java.util.Collections;
import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ChatContext {
    // all previous messages for given conversation
    private List<ChatMessageAggregate> chatContentHistory = Collections.emptyList();
    // context is used to give to AI more information about specific business domains
    // context could be parsed documents or chats history or
    // any other human-readable information
    private List<String> context = Collections.emptyList();
}
