/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.13.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
package de.vctrade.finchat.api.controller;

import de.vctrade.finchat.domain.generated.api.ApiUtil;
import de.vctrade.finchat.domain.generated.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.NativeWebRequest;
import reactor.core.publisher.Flux;

import java.util.Optional;
import java.util.UUID;


@Validated
@Tag(name = "Conversations API", description = "the Conversations API API")
public interface ConversationsApiController {

    default Optional<NativeWebRequest> getRequest() {
        return Optional.empty();
    }

    /**
     * POST /conversations/{conversation_id}/chat : Send a message and get a streaming response
     * Send a message to a conversation and get a streaming response.  This endpoint uses Server-Sent Events (SSE) to stream the assistant&#39;s response back to the client in chunks, allowing for a more interactive experience.  Args:     conversation_id: The ID of the conversation     request: The chat request containing messages  Returns:     A streaming response with the assistant&#39;s message
     *
     * @param conversationId (required)
     * @param chatRequest    (required)
     * @return Successful Response (status code 200)
     * or Validation Error (status code 422)
     */
    @Operation(
            operationId = "addChatMessageConversationsConversationIdChatPost",
            summary = "Send a message and get a streaming response",
            description = "Send a message to a conversation and get a streaming response.  This endpoint uses Server-Sent Events (SSE) to stream the assistant's response back to the client in chunks, allowing for a more interactive experience.  Args:     conversation_id: The ID of the conversation     request: The chat request containing messages  Returns:     A streaming response with the assistant's message",
            tags = {"Conversations API"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful Response", content = {
                            @Content(mediaType = "text/event-stream", schema = @Schema(implementation = ContentResponseChunk.class))
                    }),
                    @ApiResponse(responseCode = "422", description = "Validation Error")
            },
            security = {
                    @SecurityRequirement(name = "OAuth2PasswordBearer")
            }
    )
    @RequestMapping(
            method = RequestMethod.POST,
            value = "/conversations/{conversation_id}/chat",
            produces = {"text/event-stream"},
            consumes = {"application/json"}
    )

    default Flux<String> addChatMessageConversationsConversationIdChatPost(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId,
            @Parameter(name = "ChatRequest", description = "", required = true) @Valid @RequestBody ChatRequest chatRequest
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType : MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("text/event-stream"))) {
                    String exampleString = "Custom MIME type example not yet supported: text/event-stream";
                    ApiUtil.setExampleResponse(request, "text/event-stream", exampleString);
                    break;
                }
            }
        });
        return null;

    }


    /**
     * POST /conversations : Create a new conversation
     * Create a new conversation.  Returns:     The ID of the newly created conversation
     *
     * @param createConversationRequest (required)
     * @return Successful Response (status code 201)
     * or Validation Error (status code 422)
     */
    @Operation(
            operationId = "createConversationConversationsCreatePost",
            summary = "Create a new conversation",
            description = "Create a new conversation.  Returns:     The ID of the newly created conversation",
            tags = {"Conversations API"},
            responses = {
                    @ApiResponse(responseCode = "201", description = "Successful Response", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = ConversationResponse.class))
                    }),
                    @ApiResponse(responseCode = "422", description = "Validation Error")
            },
            security = {
                    @SecurityRequirement(name = "OAuth2PasswordBearer")
            }
    )
    @RequestMapping(
            method = RequestMethod.POST,
            value = "/conversations",
            produces = {"application/json"},
            consumes = {"application/json"}
    )

    default ResponseEntity<ConversationResponse> createConversationConversationsCreatePost(
            @Parameter(name = "CreateConversationRequest", description = "", required = true) @Valid @RequestBody CreateConversationRequest createConversationRequest
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType : MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"summary\" : \"summary\", \"createdAt\" : 0, \"correlationId\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"id\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"sourceIds\" : [ \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\" ] }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /conversations/{conversation_id}/chat : Get chat history for a conversation
     * Get the chat history for a conversation, excluding system messages.  Args:     conversation_id: The ID of the conversation  Returns:     Chat history with messages between user and assistant
     *
     * @param conversationId (required)
     * @return Successful Response (status code 200)
     * or Validation Error (status code 422)
     */
    @Operation(
            operationId = "getChatHistoryConversationsConversationIdChatGet",
            summary = "Get chat history for a conversation",
            description = "Get the chat history for a conversation, excluding system messages.  Args:     conversation_id: The ID of the conversation  Returns:     Chat history with messages between user and assistant",
            tags = {"Conversations API"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful Response", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = ChatHistoryResponse.class))
                    }),
                    @ApiResponse(responseCode = "422", description = "Validation Error")
            },
            security = {
                    @SecurityRequirement(name = "OAuth2PasswordBearer")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/conversations/{conversation_id}/chat",
            produces = {"application/json"}
    )

    default ResponseEntity<ChatHistoryResponse> getChatHistoryConversationsConversationIdChatGet(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType : MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"conversationId\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"messages\" : [ { \"createdAt\" : 6, \"role\" : \"user\", \"content\" : \"content\", \"order\" : 0 }, { \"createdAt\" : 6, \"role\" : \"user\", \"content\" : \"content\", \"order\" : 0 } ] }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /conversations/{conversation_id} : Get conversation details
     * Get details for a specific conversation.  Args:     conversation_id: The ID of the conversation  Returns:     Conversation details
     *
     * @param conversationId (required)
     * @return Successful Response (status code 200)
     * or Validation Error (status code 422)
     */
    @Operation(
            operationId = "getConversationConversationsConversationIdGet",
            summary = "Get conversation details",
            description = "Get details for a specific conversation.  Args:     conversation_id: The ID of the conversation  Returns:     Conversation details",
            tags = {"Conversations API"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful Response", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = ConversationResponse.class))
                    }),
                    @ApiResponse(responseCode = "422", description = "Validation Error")
            },
            security = {
                    @SecurityRequirement(name = "OAuth2PasswordBearer")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/conversations/{conversation_id}",
            produces = {"application/json"}
    )

    default ResponseEntity<ConversationResponse> getConversationConversationsConversationIdGet(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType : MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"summary\" : \"summary\", \"createdAt\" : 0, \"correlationId\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"id\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"sourceIds\" : [ \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\" ] }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }


    /**
     * GET /conversations : List all conversations. If security is enabled, only the current user&#39;s conversations are returned.
     * List all conversations.  Returns:     List of all conversations
     *
     * @param correlationId Return only conversations with this correlation ID (optional)
     * @return Successful Response (status code 200)
     * or Validation Error (status code 422)
     */
    @Operation(
            operationId = "listConversationsConversationsGet",
            summary = "List all conversations. If security is enabled, only the current user's conversations are returned.",
            description = "List all conversations.  Returns:     List of all conversations",
            tags = {"Conversations API"},
            responses = {
                    @ApiResponse(responseCode = "200", description = "Successful Response", content = {
                            @Content(mediaType = "application/json", schema = @Schema(implementation = ConversationsResponse.class))
                    }),
                    @ApiResponse(responseCode = "422", description = "Validation Error")
            },
            security = {
                    @SecurityRequirement(name = "OAuth2PasswordBearer")
            }
    )
    @RequestMapping(
            method = RequestMethod.GET,
            value = "/conversations",
            produces = {"application/json"}
    )

    default ResponseEntity<ConversationsResponse> listConversationsConversationsGet(
            @Parameter(name = "correlation_id", description = "Return only conversations with this correlation ID", in = ParameterIn.QUERY) @Valid @RequestParam(value = "correlation_id", required = false) UUID correlationId
    ) {
        getRequest().ifPresent(request -> {
            for (MediaType mediaType : MediaType.parseMediaTypes(request.getHeader("Accept"))) {
                if (mediaType.isCompatibleWith(MediaType.valueOf("application/json"))) {
                    String exampleString = "{ \"conversations\" : [ { \"summary\" : \"summary\", \"createdAt\" : 0, \"correlationId\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"id\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"sourceIds\" : [ \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\" ] }, { \"summary\" : \"summary\", \"createdAt\" : 0, \"correlationId\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"id\" : \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"sourceIds\" : [ \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\", \"046b6c7f-0b8a-43b9-b35d-6489e6daee91\" ] } ] }";
                    ApiUtil.setExampleResponse(request, "application/json", exampleString);
                    break;
                }
            }
        });
        return new ResponseEntity<>(HttpStatus.NOT_IMPLEMENTED);

    }

}
