package de.vctrade.finchat.api.service;

import de.vctrade.finchat.api.aggregate.ChatContext;
import de.vctrade.finchat.api.aggregate.MessageAggregate;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

public interface AICommunicator {
    Flux<String> streamAiResponse(@NotNull MessageAggregate message, @NotNull ChatContext context);

    // this interface probably will be changed
    Mono<String> getFullAnswer(final @NotNull String accumulatedContent);
}
