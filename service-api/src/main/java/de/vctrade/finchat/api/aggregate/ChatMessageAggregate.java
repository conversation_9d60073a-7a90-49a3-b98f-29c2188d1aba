package de.vctrade.finchat.api.aggregate;

import de.vctrade.finchat.domain.generated.dto.Role;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class ChatMessageAggregate {
    // who send chat message
    private Role role;
    // content of the message
    private String content;
    // timestamp of the message
    private Long createdAtEpochMillis;
}
