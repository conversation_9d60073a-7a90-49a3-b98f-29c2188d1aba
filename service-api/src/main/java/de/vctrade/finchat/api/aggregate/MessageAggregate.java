package de.vctrade.finchat.api.aggregate;

import de.vctrade.finchat.domain.generated.dto.ChatSettings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

import java.util.UUID;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class MessageAggregate {
    private UUID id;
    private UUID conversationId;
    private UUID userId;
    private String content;
    private ChatSettings chatSettings;
}
