package de.vctrade.finchat.api.mapper;

import de.vctrade.finchat.api.aggregate.ChatMessageAggregate;
import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.domain.generated.dto.ChatSettings;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import java.util.UUID;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-27T15:42:06+0200",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.6 (Azul Systems, Inc.)"
)
@Component
public class ChatMessageAggregateMapperImpl implements ChatMessageAggregateMapper {

    @Override
    public ChatMessageAggregate convert(MessageResponse message) {
        if ( message == null ) {
            return null;
        }

        ChatMessageAggregate.ChatMessageAggregateBuilder<?, ?> chatMessageAggregate = ChatMessageAggregate.builder();

        chatMessageAggregate.role( message.getRole() );
        chatMessageAggregate.content( message.getContent() );

        return chatMessageAggregate.build();
    }

    @Override
    public MessageAggregate convertToAggregate(UUID userId, UUID conversationId, ChatSettings chatSettings, MessageResponse message) {
        if ( userId == null && conversationId == null && chatSettings == null && message == null ) {
            return null;
        }

        MessageAggregate.MessageAggregateBuilder<?, ?> messageAggregate = MessageAggregate.builder();

        if ( message != null ) {
            messageAggregate.id( message.getId() );
            messageAggregate.content( message.getContent() );
        }
        messageAggregate.userId( userId );
        messageAggregate.conversationId( conversationId );
        messageAggregate.chatSettings( chatSettings );

        return messageAggregate.build();
    }
}
