# Document Ingestion System Integration Guide

## Overview

The Document Ingestion System provides a comprehensive solution for processing documents from various sources, extracting text content, and managing the entire ingestion workflow through an asynchronous job system.

## Architecture

```mermaid
graph TB
    subgraph "Document Ingestion System"
        UI[REST Controllers] --> Facade[DocumentIngestionFacade]
        UI --> Integration[DocumentIngestionIntegrationService]
        
        Facade --> JobService[IngestionJobService]
        Facade --> Orchestrator[JobOrchestrator]
        
        Integration --> ContentFetcher[ContentFetchingService]
        Integration --> TextExtractor[TextExtractionService]
        
        Orchestrator --> ThreadPool[ThreadPoolExecutor]
        Orchestrator --> Executor[TikaTextExtractionJobExecutor]
        
        ContentFetcher --> HttpFetcher[HttpContentFetcher]
        ContentFetcher --> FileFetcher[FileSystemContentFetcher]
        
        TextExtractor --> Tika[Apache Tika]
        
        JobService --> Persistence[(Job Database)]
        
        subgraph "Configuration"
            Props[DocumentIngestionProperties]
            Config[DocumentIngestionConfiguration]
        end
    end
```

## System Components

### 1. Configuration Layer

#### DocumentIngestionConfiguration
Main Spring configuration class that wires all components together:
- Configures content fetching services
- Sets up text extraction with Tika
- Initializes job orchestrator with thread pool
- Provides scheduled executor for monitoring

#### DocumentIngestionProperties
Comprehensive configuration properties supporting:
- Thread pool sizing and queue management
- Job retry and timeout settings
- Apache Tika parsing configuration
- File size limits and validation
- HTTP client timeout settings

### 2. Integration Layer

#### DocumentIngestionIntegrationService
High-level orchestration service providing:
- End-to-end document processing workflows
- File upload and URI-based document processing
- Comprehensive validation and error handling
- System health monitoring and metrics

### 3. Job Management Layer

#### DocumentIngestionFacade
Primary facade for job operations:
- Job creation and submission
- Status tracking and monitoring
- Cancellation and retry logic
- System status reporting

#### JobOrchestrator & ThreadPoolJobOrchestrator
Asynchronous job execution:
- Thread pool management
- Job queuing and scheduling
- Concurrent execution control
- Resource management

### 4. Processing Layer

#### ContentFetchingService
Multi-source content retrieval:
- HTTP/HTTPS URL fetching
- File system access
- Content validation and size limits
- Timeout and error handling

#### TextExtractionService & TikaTextExtractionService
Document text extraction:
- Apache Tika integration
- Multiple format support (PDF, DOC, etc.)
- Metadata extraction
- OCR capabilities (configurable)

## Configuration

### Application Properties

```properties
# Thread Pool Configuration
finchat.document.ingestion.thread-pool.core-size=4
finchat.document.ingestion.thread-pool.max-size=8
finchat.document.ingestion.thread-pool.queue-capacity=100

# Job Configuration
finchat.document.ingestion.job.max-retry-count=3
finchat.document.ingestion.job.retry-delay-millis=5000
finchat.document.ingestion.job.job-timeout-millis=300000

# Apache Tika Configuration
finchat.document.ingestion.tika.max-content-length=52428800
finchat.document.ingestion.tika.parse-timeout=60000
finchat.document.ingestion.tika.enable-ocr=false

# File Size Limits
finchat.document.ingestion.file-size.max-upload-size=104857600
finchat.document.ingestion.file-size.max-download-size=52428800

# HTTP Client Configuration
finchat.document.ingestion.http.connect-timeout-millis=10000
finchat.document.ingestion.http.read-timeout-millis=60000
finchat.document.ingestion.http.max-redirects=3
```

## Usage Examples

### 1. Document Upload Processing

```java
@Autowired
private DocumentIngestionIntegrationService integrationService;

// Process uploaded file
UUID documentId = UUID.randomUUID();
String fileName = "document.pdf";
byte[] fileBytes = uploadedFile.getBytes();
String mimeType = "application/pdf";

Mono<ExtractionResult> result = integrationService.processUploadedDocument(
    documentId, fileName, fileBytes, mimeType);

result.subscribe(
    extractionResult -> {
        String text = extractionResult.getText();
        DocumentMetadata metadata = extractionResult.getMetadata();
        // Process extracted content
    },
    error -> {
        // Handle extraction error
    }
);
```

### 2. URI-based Document Processing

```java
@Autowired
private DocumentIngestionIntegrationService integrationService;

// Process document from URL
UUID documentId = UUID.randomUUID();
URI sourceUri = URI.create("https://example.com/document.pdf");

Mono<IngestionJobEntity> jobResult = integrationService.processDocumentFromUri(
    documentId, sourceUri);

jobResult.subscribe(
    job -> {
        UUID jobId = job.getId();
        // Track job progress
        checkJobStatus(jobId);
    },
    error -> {
        // Handle job creation error
    }
);
```

### 3. Job Status Monitoring

```java
@Autowired
private DocumentIngestionIntegrationService integrationService;

// Check job status
UUID jobId = getJobId();

Mono<DocumentIngestionStatus> statusResult = integrationService.getIngestionStatus(jobId);

statusResult.subscribe(
    status -> {
        String jobStatus = status.getStatus();
        int retryCount = status.getRetryCount();
        String errorMessage = status.getErrorMessage();
        
        if ("COMPLETED".equals(jobStatus)) {
            // Job completed successfully
        } else if ("FAILED".equals(jobStatus)) {
            // Handle job failure
        }
    }
);
```

### 4. System Health Monitoring

```java
@Autowired
private DocumentIngestionIntegrationService integrationService;

// Get system health status
Mono<SystemHealthStatus> healthResult = integrationService.getSystemHealth();

healthResult.subscribe(
    health -> {
        int activeJobs = health.getActiveJobs();
        boolean healthy = health.isSystemHealthy();
        double utilization = health.getThreadPoolUtilization();
        
        // Monitor system performance
    }
);
```

## REST API Endpoints

The system exposes the following REST endpoints through the controllers:

### Document Ingestion Controller
- `POST /api/documents/upload` - Upload and process document
- `POST /api/documents/process` - Process document from URI
- `GET /api/documents/{documentId}/status` - Get processing status

### Ingestion Job Controller
- `GET /api/jobs/{jobId}` - Get job status
- `POST /api/jobs/{jobId}/retry` - Retry failed job
- `DELETE /api/jobs/{jobId}` - Cancel active job
- `GET /api/jobs/system/status` - Get system status

## Error Handling

### Common Error Scenarios

1. **File Size Exceeded**
   - Error: `IllegalArgumentException`
   - Message: "File size X exceeds maximum allowed size Y"
   - Solution: Adjust `file-size.max-upload-size` or reduce file size

2. **Unsupported File Format**
   - Error: `UnsupportedContentTypeException`
   - Solution: Check supported formats or enable additional Tika parsers

3. **Network Timeout**
   - Error: `ContentFetchingException`
   - Solution: Adjust `http.read-timeout-millis` or check network connectivity

4. **Job Timeout**
   - Error: `TimeoutException`
   - Solution: Increase `job.job-timeout-millis` or optimize processing

5. **Max Retries Exceeded**
   - Error: `IllegalStateException`
   - Solution: Check job configuration or underlying issues

### Error Recovery

The system provides automatic error recovery through:
- Configurable retry mechanisms
- Exponential backoff for transient failures
- Job status persistence for recovery after system restart
- Circuit breaker patterns for external service calls

## Performance Tuning

### Thread Pool Configuration

```properties
# For high-throughput scenarios
finchat.document.ingestion.thread-pool.core-size=8
finchat.document.ingestion.thread-pool.max-size=16
finchat.document.ingestion.thread-pool.queue-capacity=200

# For memory-constrained environments
finchat.document.ingestion.thread-pool.core-size=2
finchat.document.ingestion.thread-pool.max-size=4
finchat.document.ingestion.thread-pool.queue-capacity=50
```

### Memory Management

```properties
# Reduce memory usage for large files
finchat.document.ingestion.tika.max-content-length=26214400  # 25MB
finchat.document.ingestion.file-size.max-upload-size=52428800  # 50MB

# Enable streaming for large documents
finchat.document.ingestion.tika.enable-streaming=true
```

## Monitoring and Metrics

### Key Metrics to Monitor

1. **Job Metrics**
   - Active job count
   - Job completion rate
   - Average processing time
   - Retry frequency

2. **System Metrics**
   - Thread pool utilization
   - Queue depth
   - Memory usage
   - CPU utilization

3. **Error Metrics**
   - Error rate by type
   - Failed job count
   - Timeout frequency

### Health Checks

The system provides health check endpoints:
- `/actuator/health` - Overall system health
- `/actuator/health/disk` - Disk space availability
- `/actuator/health/thread-pool` - Thread pool status

## Testing

### Integration Tests

Run the comprehensive integration tests:

```bash
mvn test -Dtest=DocumentIngestionIntegrationTest
mvn test -Dtest=JobSystemIntegrationTest
```

### Test Configuration

For testing, use reduced timeouts and limits:

```properties
finchat.document.ingestion.thread-pool.core-size=1
finchat.document.ingestion.job.retry-delay-millis=100
finchat.document.ingestion.tika.parse-timeout=5000
```

## Troubleshooting

### Common Issues

1. **Jobs stuck in PENDING state**
   - Check thread pool configuration
   - Verify job executor is running
   - Look for blocking operations in job execution

2. **High memory usage**
   - Reduce `tika.max-content-length`
   - Check for memory leaks in custom components
   - Monitor large file processing

3. **Slow text extraction**
   - Optimize Tika configuration
   - Consider enabling parallel processing
   - Use document-specific parsers

4. **Network connectivity issues**
   - Check firewall settings
   - Verify SSL certificate validation
   - Adjust timeout configurations

### Debug Logging

Enable debug logging for troubleshooting:

```properties
logging.level.de.vctrade.finchat.service.job=DEBUG
logging.level.de.vctrade.finchat.service.extraction=DEBUG
logging.level.de.vctrade.finchat.service.fetching=DEBUG
logging.level.org.apache.tika=DEBUG
```

## Security Considerations

1. **File Upload Security**
   - Validate file types and extensions
   - Scan for malware before processing
   - Implement rate limiting

2. **Network Security**
   - Use HTTPS for external document fetching
   - Validate SSL certificates
   - Implement URL whitelist for allowed domains

3. **Data Privacy**
   - Ensure extracted text is properly secured
   - Implement data retention policies
   - Consider encryption for sensitive documents

## Migration from Legacy Configuration

If migrating from the legacy `JobConfig` system:

1. Update property prefixes from `finchat.job` to `finchat.document.ingestion`
2. Migrate thread pool configuration to new structure
3. Add new configuration options for file size limits and Tika settings
4. Update dependency injection to use new configuration beans

The system maintains backward compatibility through the legacy properties in `application.properties`.
