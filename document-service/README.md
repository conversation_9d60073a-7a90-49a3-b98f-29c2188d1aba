# Document Service - Text Extraction

This module provides Apache Tika-based text extraction capabilities for the finchat-service application.

## Features

### Supported Document Formats
- **PDF** - Portable Document Format files
- **Microsoft Word** - DOCX and DOC files
- **Text Files** - Plain text, HTML, XML, CSV
- **Microsoft PowerPoint** - PPTX and PPT files  
- **Microsoft Excel** - XLSX and XLS files
- **Rich Text Format** - RTF files

### Text Extraction Capabilities

#### TikaTextExtractionService
- **Smart Content Detection**: Automatically detects document format using Apache Tika's AutoDetectParser
- **Metadata Extraction**: Extracts document properties like title, author, creation date, page count
- **Intelligent Chunking**: Breaks text into logical chunks with semantic overlap for better processing
- **Reactive Processing**: Returns `Mono<ExtractionResult>` for non-blocking operations
- **Error Handling**: Proper exception handling for unsupported formats and parsing errors

#### Text Chunking Features
- **Semantic Chunking**: Splits text at paragraph boundaries for better context preservation
- **Configurable Chunk Size**: Default 2000 characters with 200 character overlap
- **Sentence Boundary Detection**: Attempts to break chunks at sentence boundaries when possible
- **Offset Tracking**: Maintains character offsets for precise text location

#### Extracted Metadata
- Document title, author, subject
- Creation and modification dates
- Page count (where applicable)
- Document language detection
- Character encoding information
- Custom document properties

## Usage Example

```java
@Service
public class DocumentProcessingService {
    
    private final TextExtractionService textExtractionService;
    
    public DocumentProcessingService(TextExtractionService textExtractionService) {
        this.textExtractionService = textExtractionService;
    }
    
    public Mono<String> processDocument(InputStream inputStream, String contentType, String filename) {
        return textExtractionService.extractText(inputStream, contentType, filename)
                .map(result -> {
                    // Process extracted text
                    String text = result.getText();
                    List<TextChunk> chunks = result.getChunks();
                    DocumentMetadata metadata = result.getMetadata();
                    
                    // Your processing logic here
                    return processExtractedContent(text, chunks, metadata);
                });
    }
}
```

## Configuration

The service is automatically configured as a Spring `@Service` and can be injected wherever text extraction is needed.

### Dependencies
- `org.apache.tika:tika-core:2.9.1`
- `org.apache.tika:tika-parsers-standard-package:2.9.1`

## Error Handling

- **UnsupportedContentTypeException**: Thrown when attempting to extract from unsupported file types
- **TextExtractionException**: General extraction failure with cause information
- **Reactive Error Handling**: All errors are propagated through the reactive stream

## Performance Considerations

- Text extraction runs on `Schedulers.boundedElastic()` to prevent blocking
- Large documents are processed efficiently with streaming parsing
- Memory usage is optimized through Tika's streaming architecture
- Chunk overlap helps maintain context across processing boundaries
