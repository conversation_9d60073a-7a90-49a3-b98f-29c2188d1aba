package de.vctrade.finchat.integration;

import de.vctrade.finchat.config.DocumentIngestionConfiguration;
import de.vctrade.finchat.config.DocumentIngestionProperties;
import de.vctrade.finchat.model.entity.IngestionJobEntity;
import de.vctrade.finchat.ingest.application.handler.DocumentIngestionIntegrationHandler;
import de.vctrade.finchat.service.extraction.ExtractionResult;
import de.vctrade.finchat.ingest.application.handler.DocumentIngestionFacade;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.UUID;

/**
 * Integration tests for the complete document ingestion system.
 * Tests the end-to-end flow from document upload to text extraction.
 */
@SpringBootTest(classes = {
    DocumentIngestionConfiguration.class,
    DocumentMetadataIngestionIntegrationTest.TestConfiguration.class
})
@EnableConfigurationProperties(DocumentIngestionProperties.class)
@TestPropertySource(properties = {
    "finchat.document.ingestion.thread-pool.core-size=2",
    "finchat.document.ingestion.thread-pool.max-size=4",
    "finchat.document.ingestion.tika.max-content-length=10485760",
    "finchat.document.ingestion.file-size.max-upload-size=10485760"
})
public class DocumentMetadataIngestionIntegrationTest {

    @Autowired
    private DocumentIngestionIntegrationHandler integrationService;

    @Autowired
    private DocumentIngestionFacade ingestionFacade;

    @Autowired
    private DocumentIngestionProperties properties;

    private UUID testDocumentId;

    @BeforeEach
    void setUp() {
        testDocumentId = UUID.randomUUID();
    }

    @Test
    void testProcessUploadedTextDocument() {
        // Given
        String fileName = "test-document.txt";
        String content = "This is a test document with some content for text extraction testing.";
        byte[] fileBytes = content.getBytes(StandardCharsets.UTF_8);
        String mimeType = "text/plain";

        // When & Then
        Mono<ExtractionResult> result = integrationService.processUploadedDocument(
            testDocumentId, fileName, fileBytes, mimeType);

        StepVerifier.create(result)
            .assertNext(extractionResult -> {
                assert extractionResult != null;
                assert extractionResult.getText().contains("test document");
                assert extractionResult.getMetadata() != null;
                assert extractionResult.getMetadata().getContentType().contains("text");
            })
            .verifyComplete();
    }

    @Test
    void testProcessUploadedPdfDocument() {
        // Given - Simple PDF content (in practice, use a real PDF)
        String fileName = "test-document.pdf";
        byte[] pdfBytes = createSimplePdfBytes();
        String mimeType = "application/pdf";

        // When & Then
        Mono<ExtractionResult> result = integrationService.processUploadedDocument(
            testDocumentId, fileName, pdfBytes, mimeType);

        StepVerifier.create(result)
            .assertNext(extractionResult -> {
                assert extractionResult != null;
                assert extractionResult.getText() != null;
                assert extractionResult.getMetadata() != null;
            })
            .verifyComplete();
    }

    @Test
    void testFileSizeValidation() {
        // Given - File too large
        String fileName = "large-document.txt";
        long maxSize = properties.getFileSize().getMaxUploadSize();
        byte[] largeByes = new byte[(int) (maxSize + 1000)];
        String mimeType = "text/plain";

        // When & Then
        Mono<ExtractionResult> result = integrationService.processUploadedDocument(
            testDocumentId, fileName, largeByes, mimeType);

        StepVerifier.create(result)
            .expectError(IllegalArgumentException.class)
            .verify();
    }

    @Test
    void testDocumentIngestionJobCreation() {
        // Given
        URI sourceUri = URI.create("file:///tmp/test-document.txt");

        // When & Then
        Mono<IngestionJobEntity> result = integrationService.processDocumentFromUri(
            testDocumentId, sourceUri);

        StepVerifier.create(result)
            .assertNext(job -> {
                assert job != null;
                assert job.getId() != null;
                assert job.getDocumentId().equals(testDocumentId);
                assert job.getStatus() != null;
            })
            .verifyComplete();
    }

    @Test
    void testInvalidUriValidation() {
        // Given - Invalid URI scheme
        URI invalidUri = URI.create("ftp://example.com/document.pdf");

        // When & Then
        Mono<IngestionJobEntity> result = integrationService.processDocumentFromUri(
            testDocumentId, invalidUri);

        StepVerifier.create(result)
            .expectError(IllegalArgumentException.class)
            .verify();
    }

    @Test
    void testJobStatusTracking() {
        // Given
        URI sourceUri = URI.create("file:///tmp/test-document.txt");

        // When
        Mono<DocumentIngestionIntegrationHandler.DocumentIngestionStatus> statusResult =
            integrationService.processDocumentFromUri(testDocumentId, sourceUri)
                .flatMap(job -> integrationService.getIngestionStatus(job.getId()));

        // Then
        StepVerifier.create(statusResult)
            .assertNext(status -> {
                assert status != null;
                assert status.getJobId() != null;
                assert status.getDocumentId().equals(testDocumentId);
                assert status.getStatus() != null;
                assert status.getCreatedAt() != null;
            })
            .verifyComplete();
    }

    @Test
    void testSystemHealthMonitoring() {
        // When
        Mono<DocumentIngestionIntegrationHandler.SystemHealthStatus> healthResult =
            integrationService.getSystemHealth();

        // Then
        StepVerifier.create(healthResult)
            .assertNext(health -> {
                assert health != null;
                assert health.getActiveJobs() >= 0;
                // Note: In real implementation, more health checks would be performed
            })
            .verifyComplete();
    }

    @Test
    void testConcurrentDocumentProcessing() {
        // Given
        String content1 = "First test document content";
        String content2 = "Second test document content";
        byte[] bytes1 = content1.getBytes(StandardCharsets.UTF_8);
        byte[] bytes2 = content2.getBytes(StandardCharsets.UTF_8);

        // When
        Mono<ExtractionResult> result1 = integrationService.processUploadedDocument(
            UUID.randomUUID(), "doc1.txt", bytes1, "text/plain");

        Mono<ExtractionResult> result2 = integrationService.processUploadedDocument(
            UUID.randomUUID(), "doc2.txt", bytes2, "text/plain");

        // Then
        StepVerifier.create(Mono.zip(result1, result2))
            .assertNext(tuple -> {
                ExtractionResult r1 = tuple.getT1();
                ExtractionResult r2 = tuple.getT2();

                assert r1.getText().contains("First test");
                assert r2.getText().contains("Second test");
            })
            .verifyComplete();
    }

    @Test
    void testExtractionTimeout() {
        // Given - Properties configured with short timeout for testing
        properties.getTika().setParseTimeout(100); // Very short timeout

        String fileName = "slow-document.txt";
        byte[] fileBytes = "Simple content".getBytes(StandardCharsets.UTF_8);
        String mimeType = "text/plain";

        // When & Then
        Mono<ExtractionResult> result = integrationService.processUploadedDocument(
            testDocumentId, fileName, fileBytes, mimeType);

        // Reset timeout after test
        StepVerifier.create(result)
            .assertNext(extractionResult -> {
                // Should complete quickly for simple text
                assert extractionResult != null;
            })
            .verifyComplete();

        properties.getTika().setParseTimeout(60000); // Reset to default
    }

    // Helper methods

    private byte[] createSimplePdfBytes() {
        // This would create actual PDF bytes in a real implementation
        // For testing purposes, return minimal PDF-like structure
        String pdfHeader = "%PDF-1.4\n";
        String pdfContent = "1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n";
        String pdfEnd = "%%EOF\n";

        return (pdfHeader + pdfContent + pdfEnd).getBytes(StandardCharsets.UTF_8);
    }

    /**
     * Test configuration for mocking dependencies
     */
    @org.springframework.boot.test.context.TestConfiguration
    static class TestConfiguration {

        // Mock beans would be configured here in a real implementation
        // For now, using the actual implementation classes

    }
}
