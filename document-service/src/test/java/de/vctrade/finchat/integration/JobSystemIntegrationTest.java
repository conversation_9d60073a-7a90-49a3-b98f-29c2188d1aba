package de.vctrade.finchat.integration;

import de.vctrade.finchat.model.entity.IngestionJobEntity;
import de.vctrade.finchat.ingest.application.handler.DocumentIngestionFacade;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import de.vctrade.finchat.ingest.domain.service.JobOrchestrator;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Integration tests for the job management system.
 * Tests job creation, execution, status tracking, and error handling.
 */
@SpringBootTest
@TestPropertySource(properties = {
    "finchat.document.ingestion.thread-pool.core-size=2",
    "finchat.document.ingestion.job.max-retry-count=2",
    "finchat.document.ingestion.job.retry-delay-millis=1000"
})
public class JobSystemIntegrationTest {

    @Autowired
    private DocumentIngestionFacade ingestionFacade;

    @Autowired
    private IngestionJobService ingestionJobService;

    @Autowired
    private JobOrchestrator jobOrchestrator;

    @Test
    void testJobCreationAndExecution() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When
        Mono<IngestionJobEntity> jobResult = ingestionFacade.startDocumentIngestion(documentId);

        // Then
        StepVerifier.create(jobResult)
            .assertNext(job -> {
                assert job != null;
                assert job.getId() != null;
                assert job.getDocumentId().equals(documentId);
                assert job.getStatus() != null;
                assert job.getCreatedAt() != null;
                assert job.getRetryCount() == 0;
            })
            .verifyComplete();
    }

    @Test
    void testJobStatusTracking() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When
        Mono<IngestionJobEntity> statusResult = ingestionFacade.startDocumentIngestion(documentId)
            .delayElement(Duration.ofMillis(100)) // Allow job to start
            .flatMap(job -> ingestionFacade.getJobStatus(job.getId()));

        // Then
        StepVerifier.create(statusResult)
            .assertNext(job -> {
                assert job != null;
                assert job.getDocumentId().equals(documentId);
                assert job.getUpdatedAt() != null;
            })
            .verifyComplete();
    }

    @Test
    void testJobCancellation() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When
        Mono<Boolean> cancellationResult = ingestionFacade.startDocumentIngestion(documentId)
            .flatMap(job -> ingestionFacade.cancelIngestionJob(job.getId()));

        // Then
        StepVerifier.create(cancellationResult)
            .assertNext(cancelled -> {
                assert cancelled != null;
                // Result depends on job state when cancellation is attempted
            })
            .verifyComplete();
    }

    @Test
    void testJobRetry() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When
        Mono<IngestionJobEntity> retryResult = ingestionFacade.startDocumentIngestion(documentId)
            .flatMap(job -> {
                // Simulate job failure and retry
                return ingestionFacade.retryIngestionJob(job.getId());
            });

        // Then
        StepVerifier.create(retryResult)
            .assertNext(job -> {
                // Job should exist if retry is allowed
                if (job != null) {
                    assert job.getRetryCount() >= 0;
                }
            })
            .verifyComplete();
    }

    @Test
    void testSystemStatusMonitoring() {
        // When
        Mono<DocumentIngestionFacade.JobSystemStatus> statusResult =
            ingestionFacade.getSystemStatus();

        // Then
        StepVerifier.create(statusResult)
            .assertNext(status -> {
                assert status != null;
                assert status.getActiveJobCount() >= 0;
                assert status.isHealthy(); // Should be healthy in test environment
            })
            .verifyComplete();
    }

    @Test
    void testConcurrentJobExecution() throws InterruptedException {
        // Given
        int numberOfJobs = 5;
        CountDownLatch latch = new CountDownLatch(numberOfJobs);

        // When
        for (int i = 0; i < numberOfJobs; i++) {
            UUID documentId = UUID.randomUUID();
            ingestionFacade.startDocumentIngestion(documentId)
                .subscribe(
                    job -> latch.countDown(),
                    error -> latch.countDown()
                );
        }

        // Then
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assert completed : "Not all jobs completed within timeout";
    }

    @Test
    void testJobOrchestratorActiveJobCount() {
        // Given
        UUID documentId = UUID.randomUUID();
        int initialActiveJobs = jobOrchestrator.getActiveJobCount();

        // When
        Mono<Void> jobSubmission = ingestionFacade.startDocumentIngestion(documentId)
            .then(Mono.delay(Duration.ofMillis(50))) // Allow job to start
            .then();

        // Then
        StepVerifier.create(jobSubmission)
            .verifyComplete();

        // Verify active job count changed (may have completed by now)
        int finalActiveJobs = jobOrchestrator.getActiveJobCount();
        assert finalActiveJobs >= initialActiveJobs : "Active job count should not decrease";
    }

    @Test
    void testNonExistentJobStatus() {
        // Given
        UUID nonExistentJobId = UUID.randomUUID();

        // When
        Mono<IngestionJobEntity> statusResult = ingestionFacade.getJobStatus(nonExistentJobId);

        // Then
        StepVerifier.create(statusResult)
            .verifyComplete(); // Should complete empty for non-existent job
    }

    @Test
    void testJobExecutionWithTimeout() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When - Start job and wait for processing
        Mono<IngestionJobEntity> jobResult = ingestionFacade.startDocumentIngestion(documentId)
            .delayElement(Duration.ofSeconds(1)) // Allow time for processing
            .flatMap(job -> ingestionFacade.getJobStatus(job.getId()));

        // Then
        StepVerifier.create(jobResult)
            .assertNext(job -> {
                assert job != null;
                // Job status should be updated after processing attempt
                assert job.getUpdatedAt() != null;
            })
            .verifyComplete();
    }

    @Test
    void testMultipleJobStatusChecks() {
        // Given
        UUID documentId = UUID.randomUUID();

        // When
        Mono<Boolean> multipleChecks = ingestionFacade.startDocumentIngestion(documentId)
            .flatMap(job -> {
                // Check status multiple times
                return Mono.zip(
                    ingestionFacade.getJobStatus(job.getId()),
                    ingestionFacade.getJobStatus(job.getId()),
                    ingestionFacade.getJobStatus(job.getId())
                ).map(tuple -> true);
            });

        // Then
        StepVerifier.create(multipleChecks)
            .assertNext(result -> assert result)
            .verifyComplete();
    }
}
