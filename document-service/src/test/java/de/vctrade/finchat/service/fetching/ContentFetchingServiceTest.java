package de.vctrade.finchat.service.fetching;

import de.vctrade.finchat.ingest.adapter.service.ContentFetchingServiceImpl;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import de.vctrade.finchat.ingest.domain.service.ContentFetcher;
import de.vctrade.finchat.ingest.domain.service.UnsupportedSourceException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.net.URI;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test class for ContentFetchingService implementations.
 */
class ContentFetchingServiceTest {

    private ContentFetchingServiceImpl contentFetchingService;

    @Mock
    private ContentFetcher httpFetcher;

    @Mock
    private ContentFetcher fileFetcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        contentFetchingService = new ContentFetchingServiceImpl(List.of(httpFetcher, fileFetcher));
    }

    @Test
    void shouldFetchContentFromSupportedSource() {
        // Given
        URI source = URI.create("https://example.com/document.pdf");
        FetchRequest request = FetchRequest.builder()
                .source(source)
                .build();

        FetchResult expectedResult = FetchResult.builder()
                .contentStream(new ByteArrayInputStream("test content".getBytes()))
                .source(source)
                .contentType("application/pdf")
                .contentLength(12L)
                .build();

        when(httpFetcher.supports(source)).thenReturn(true);
        when(fileFetcher.supports(source)).thenReturn(false);
        when(httpFetcher.fetch(request)).thenReturn(Mono.just(expectedResult));

        // When & Then
        StepVerifier.create(contentFetchingService.fetchContent(request))
                .expectNext(expectedResult)
                .verifyComplete();

        verify(httpFetcher).fetch(request);
        verify(fileFetcher, never()).fetch(any());
    }

    @Test
    void shouldReturnErrorForUnsupportedSource() {
        // Given
        URI source = URI.create("ftp://example.com/document.pdf");
        FetchRequest request = FetchRequest.builder()
                .source(source)
                .build();

        when(httpFetcher.supports(source)).thenReturn(false);
        when(fileFetcher.supports(source)).thenReturn(false);

        // When & Then
        StepVerifier.create(contentFetchingService.fetchContent(request))
                .expectError(UnsupportedSourceException.class)
                .verify();
    }

    @Test
    void shouldReturnTrueForSupportedScheme() {
        // Given
        URI source = URI.create("https://example.com/document.pdf");
        when(httpFetcher.supports(source)).thenReturn(true);
        when(fileFetcher.supports(source)).thenReturn(false);

        // When
        boolean canFetch = contentFetchingService.canFetch(source);

        // Then
        assertTrue(canFetch);
    }

    @Test
    void shouldReturnFalseForUnsupportedScheme() {
        // Given
        URI source = URI.create("ftp://example.com/document.pdf");
        when(httpFetcher.supports(source)).thenReturn(false);
        when(fileFetcher.supports(source)).thenReturn(false);

        // When
        boolean canFetch = contentFetchingService.canFetch(source);

        // Then
        assertFalse(canFetch);
    }

    @Test
    void shouldReturnAllSupportedSchemes() {
        // Given
        when(httpFetcher.getSupportedSchemes()).thenReturn(new String[]{"http", "https"});
        when(fileFetcher.getSupportedSchemes()).thenReturn(new String[]{"file"});

        // When
        String[] supportedSchemes = contentFetchingService.getSupportedSchemes();

        // Then
        assertEquals(3, supportedSchemes.length);
        assertTrue(List.of(supportedSchemes).contains("http"));
        assertTrue(List.of(supportedSchemes).contains("https"));
        assertTrue(List.of(supportedSchemes).contains("file"));
    }
}
