package de.vctrade.finchat.service.extraction;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.io.ByteArrayInputStream;
import java.io.InputStream;

import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
class TikaTextExtractionServiceTest {

    private TikaTextExtractionService textExtractionService;

    @BeforeEach
    void setUp() {
        textExtractionService = new TikaTextExtractionService();
    }

    @Test
    void shouldSupportCommonContentTypes() {
        assertThat(textExtractionService.supportsContentType("application/pdf")).isTrue();
        assertThat(textExtractionService.supportsContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document")).isTrue();
        assertThat(textExtractionService.supportsContentType("text/plain")).isTrue();
        assertThat(textExtractionService.supportsContentType("application/octet-stream")).isFalse();
    }

    @Test
    void shouldExtractTextFromPlainText() {
        String content = "This is a test document with some content.";
        InputStream inputStream = new ByteArrayInputStream(content.getBytes());

        StepVerifier.create(textExtractionService.extractText(inputStream, "text/plain", "test.txt"))
                .assertNext(result -> {
                    assertThat(result.getText()).contains("test document");
                    assertThat(result.getMetadata().getFilename()).isEqualTo("test.txt");
                    assertThat(result.getMetadata().getContentType()).isEqualTo("text/plain");
                    assertThat(result.getChunks()).isNotEmpty();
                })
                .verifyComplete();
    }

    @Test
    void shouldHandleUnsupportedContentType() {
        InputStream inputStream = new ByteArrayInputStream("test".getBytes());

        StepVerifier.create(textExtractionService.extractText(inputStream, "application/octet-stream", "test.bin"))
                .expectError(UnsupportedContentTypeException.class)
                .verify();
    }
}
