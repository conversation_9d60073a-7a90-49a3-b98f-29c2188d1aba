# Document Ingestion Configuration
# =================================

# Thread Pool Configuration
finchat.document.ingestion.thread-pool.core-size=4
finchat.document.ingestion.thread-pool.max-size=8
finchat.document.ingestion.thread-pool.queue-capacity=100

# Job Configuration
finchat.document.ingestion.job.max-retry-count=3
finchat.document.ingestion.job.retry-delay-millis=5000
finchat.document.ingestion.job.job-timeout-millis=300000

# Apache Tika Configuration
finchat.document.ingestion.tika.max-content-length=52428800
finchat.document.ingestion.tika.parse-timeout=60000
finchat.document.ingestion.tika.enable-ocr=false

# File Size Limits
finchat.document.ingestion.file-size.max-upload-size=104857600
finchat.document.ingestion.file-size.max-download-size=52428800

# HTTP Client Configuration
finchat.document.ingestion.http.connect-timeout-millis=10000
finchat.document.ingestion.http.read-timeout-millis=60000
finchat.document.ingestion.http.max-redirects=3

# Legacy Job Configuration (for backwards compatibility)
finchat.job.thread-pool.size=4
finchat.job.thread-pool.queue-capacity=100
finchat.job.max-retry-count=3
finchat.job.retry-delay-millis=5000

# Spring Boot Configuration
spring.application.name=finchat-document-service
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=4
spring.task.scheduling.pool.size=2

# Logging Configuration
logging.level.de.vctrade.finchat=INFO
logging.level.org.apache.tika=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# Management and Monitoring
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
management.metrics.enable.all=true
