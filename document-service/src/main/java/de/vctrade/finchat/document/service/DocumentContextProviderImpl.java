package de.vctrade.finchat.document.service;

import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.document.service.api.DocumentContextProvider;
import jakarta.validation.constraints.NotNull;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class DocumentContextProviderImpl implements DocumentContextProvider {
    @Override
    public @NotNull List<String> provide(@NotNull MessageAggregate aggregate) {
        return Collections.emptyList();
    }
}
