package de.vctrade.finchat.controller;

import de.vctrade.base.security.core.userdetails.Caller;
import de.vctrade.finchat.api.controller.ConversationsApiController;
import de.vctrade.finchat.api.mapper.ChatMessageAggregateMapper;
import de.vctrade.finchat.api.service.AICommunicationManager;
import de.vctrade.finchat.api.service.api.ChatMessageRepository;
import de.vctrade.finchat.api.service.api.ConversationRepository;
import de.vctrade.finchat.domain.generated.dto.*;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@AllArgsConstructor
public class ConversationsApiControllerImpl implements ConversationsApiController {

    private final ConversationRepository conversationRepository;
    private final ChatMessageRepository chatMessageRepository;
    private final AICommunicationManager aiCommunicationManager;
    private final ChatMessageAggregateMapper mapper;

    @Override
    public ResponseEntity<ConversationResponse> createConversationConversationsCreatePost(
            @Parameter(name = "CreateConversationRequest", description = "", required = true)@Valid @RequestBody CreateConversationRequest createConversationRequest
    ) {
        try {
            var conversation = conversationRepository.save(createConversationRequest);

            return ResponseEntity.status(HttpStatus.CREATED).body(conversation);
        } catch (Exception error) {
            log.error("Cannot persist conversation", error);
        }

        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();

    }

    @Override
    public ResponseEntity<ConversationResponse> getConversationConversationsConversationIdGet(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId
    ) {
        try {
            var conversation = conversationRepository.fetchID(conversationId);
            if (conversation != null) {
                return ResponseEntity.status(HttpStatus.OK).body(conversation);
            }
        } catch (Exception error) {
            log.error("Cannot get conversation", error);
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();

    }


    @Override
    public ResponseEntity<ConversationsResponse> listConversationsConversationsGet(
            @Parameter(name = "correlation_id", description = "Return only conversations with this correlation ID", in = ParameterIn.QUERY) @Valid @RequestParam(value = "correlation_id", required = false) UUID correlationId
    ) {
        try {
            var conversations = correlationId != null ? conversationRepository.fetchByReferenceId(correlationId) : conversationRepository.fetchAll();

            return ResponseEntity.status(HttpStatus.OK).body(new ConversationsResponse(conversations));
        } catch (Exception error) {
            log.error("Cannot get conversation's list", error);
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();

    }

    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public Flux<String> addChatMessageConversationsConversationIdChatPost(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId,
            @Parameter(name = "ChatRequest", description = "", required = true) @Valid @RequestBody ChatRequest chatRequest
    ) {

        var authentication = SecurityContextHolder.getContext().getAuthentication();

        return Mono.just(authentication).flatMapMany(auth -> {

            var caller = (Caller) auth.getPrincipal();
            return Mono.fromCallable(() ->
                            // save to db is blocking process ...
                            chatMessageRepository.save(caller.getId(), conversationId, chatRequest.getMessage())
                    )
                    .subscribeOn(Schedulers.boundedElastic())
                    .flatMapMany(chatMessage -> aiCommunicationManager.chatWithAI(mapper.convertToAggregate(caller.getId(), conversationId, chatRequest.getMessage().getChatSettings(), chatMessage)));
        });
    }

    @Override

    public ResponseEntity<ChatHistoryResponse> getChatHistoryConversationsConversationIdChatGet(
            @Parameter(name = "conversation_id", description = "", required = true, in = ParameterIn.PATH) @PathVariable("conversation_id") UUID conversationId
    ) {
        try {
            var messages = chatMessageRepository.fetchByConversationId(conversationId);
            for (int i = 0; i < messages.size(); i++) {
                messages.get(i).setOrder(i + 1);
            }

            if (!messages.isEmpty()) {
                return ResponseEntity.status(HttpStatus.OK).body(ChatHistoryResponse.builder().conversationId(conversationId).messages(messages).build());
            }
        } catch (Exception error) {
            log.error("Cannot get conversation", error);
        }

        return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
    }

}
