package de.vctrade.finchat.service.ai.chat;

import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.api.service.AICommunicator;
import de.vctrade.finchat.api.aggregate.ChatContext;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class AIEmulatorImpl implements AICommunicator {
    // Simulates an external service returning a stream of content chunks
    @Override
    public Flux<String> streamAiResponse(final @NotNull MessageAggregate message, final @NotNull ChatContext context) {
        final var chunks = Arrays.asList(
                "Hi everyone ! ",
                "I am an AI emulator. ",
                "How can I help you today?",
                "Is there anything specific you'd like to discuss?"
        );

        return Flux.fromIterable(chunks)
                .delayElements(Duration.ofMillis(150)) // Simulate chunks arriving over time
                .doOnNext(chunk -> log.debug("AIEmulatorImpl service sending chunk: {}", chunk));
    }

    //not sure, how to handle full response yet ...
    public Mono<String> getFullAnswer(final @NotNull String accumulatedContent) {
        return Mono.just(accumulatedContent.trim())
                .delayElement(Duration.ofMillis(100));
    }
}
