package de.vctrade.finchat.service.ai.context;

import de.vctrade.finchat.api.aggregate.ChatContext;
import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.api.mapper.ChatMessageAggregateMapper;
import de.vctrade.finchat.api.service.api.ChatMessageRepository;
import de.vctrade.finchat.document.service.api.DocumentContextProvider;
import de.vctrade.finchat.service.ai.context.api.ChatContextProvider;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class ChatContextProviderImpl implements ChatContextProvider {

    private final ChatMessageRepository chatMessageRepository;
    private final ChatMessageAggregateMapper mapper;
    private final DocumentContextProvider documentContextProvider;

    @Override
    public ChatContext provide(final @NotNull MessageAggregate chatMessage){
        final var chatContentHistory = chatMessageRepository.fetchByConversationId(chatMessage.getConversationId()).stream().map(
                mapper::convert
        ).toList();
        final var documentContext = documentContextProvider.provide(chatMessage);
        return ChatContext.builder().chatContentHistory(chatContentHistory).context(documentContext).build();
    }
}
