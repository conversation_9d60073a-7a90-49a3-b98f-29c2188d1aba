package de.vctrade.finchat.service.ai.chat;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.vctrade.finchat.api.aggregate.MessageAggregate;
import de.vctrade.finchat.api.service.AICommunicationManager;
import de.vctrade.finchat.api.service.AICommunicator;
import de.vctrade.finchat.api.service.api.ChatMessageRepository;
import de.vctrade.finchat.domain.generated.dto.ContentResponseChunk;
import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import de.vctrade.finchat.domain.generated.dto.ResponseChunkType;
import de.vctrade.finchat.domain.generated.dto.Role;
import de.vctrade.finchat.service.ai.context.api.ChatContextProvider;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@AllArgsConstructor
@Service
public class AICommunicationManagerImpl implements AICommunicationManager {


    private final AICommunicator aiCommunicator;
    private final ChatContextProvider chatContextProvider;
    private final ChatMessageRepository repository;
    private final ObjectMapper objectMapper;

    @Override
    public Flux<String> chatWithAI(
            final @NotNull MessageAggregate message) {


        final var chatContext = chatContextProvider.provide(message);

        final ContentResponseChunk startEvent = ContentResponseChunk.builder().type(ResponseChunkType.START).messageId(message.getId()).build();
        final Flux<ContentResponseChunk> startEventFlux = Flux.just(startEvent);

        final Flux<ContentResponseChunk> contentEventsFlux = aiCommunicator.streamAiResponse(message, chatContext)
                .map(chunkContent -> ContentResponseChunk.builder().messageId(message.getId()).type(ResponseChunkType.CONTENT).content(chunkContent).build());

        final Mono<String> fullAnswerMono = contentEventsFlux
                .map(ContentResponseChunk::getContent)
                .reduce(new StringBuilder(), StringBuilder::append)
                .map(StringBuilder::toString)
                .flatMap(aiCommunicator::getFullAnswer);

        final Flux<ContentResponseChunk> endEventFlux = fullAnswerMono
                .map(fullContent -> ContentResponseChunk.builder().type(ResponseChunkType.END).messageId(message.getId()).content(fullContent).build())
                .flux();

        return Flux.concat(startEventFlux, contentEventsFlux, endEventFlux)
                .map(chunk -> {
                    try {
                        if(ResponseChunkType.END == chunk.getType()) {
                            // persist last answer from AI, because it contains full info
                            repository.save(message.getUserId(), message.getConversationId(), MessageRequest.builder().role(Role.ASSISTANT).content(chunk.getContent()).chatSettings(message.getChatSettings()).build());
                        }

                        // Serialize ContentResponseChunk object to JSON string
                        return objectMapper.writeValueAsString(chunk);
                    } catch (JsonProcessingException e) {
                        log.error("Error serializing chunk: {}", e.getMessage());
                        return "{\"type\":\"error\", \"message\":\"Serialization error\"}";
                    }
                })
                .doOnSubscribe(subscription -> log.info("SSE Stream for conversation {} subscribed.", message.getConversationId()))
                .doFinally(signalType -> log.info("SSE Stream for conversation {}} completed with signal: {}",message.getConversationId(),  signalType));
    }

}
