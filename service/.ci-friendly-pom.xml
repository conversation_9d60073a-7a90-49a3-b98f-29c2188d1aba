<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>de.vctrade</groupId>
        <artifactId>finchat-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>finchat-service-service</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.war.name>finchat-service</project.war.name>
        <jgiven.properties.location>target/test-classes/jgiven.properties</jgiven.properties.location>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.tngtech.jgiven</groupId>
            <artifactId>jgiven-spring-junit5</artifactId>
            <version>${jgiven.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>finchat-domain</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>finchat-persistence-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>finchat-service-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>document-service-api</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${org.projectlombok.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>base-security</artifactId>
            <version>${vctrade.base.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
            <version>${org.springdoc.versions}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mongodb</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-datatype</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <finalName>${project.war.name}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven.surefire.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven.war.plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>${maven.jar.plugin.version}</version>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <packageName>de.vctrade.synloan</packageName>
                            <mainClass>de.vctrade.app.AuthorizationServiceApp</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.tngtech.jgiven</groupId>
                <artifactId>jgiven-maven-plugin</artifactId>
                <version>${jgiven.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <format>html</format>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>