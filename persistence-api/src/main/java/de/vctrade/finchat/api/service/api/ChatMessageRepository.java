package de.vctrade.finchat.api.service.api;

import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.UUID;

public interface ChatMessageRepository {

    @NotNull
    MessageResponse save(final @NotNull UUID conversationId, final @NotNull MessageRequest chatMessage);

    @NotNull
    MessageResponse save(final @NotNull UUID userId, final @NotNull UUID conversationId, final @NotNull MessageRequest chatMessage);

    @NotNull List<MessageResponse> fetchAll();

    @NotNull List<MessageResponse> fetchByConversationId(final @NotNull UUID conversationId);

    @Nullable
    MessageResponse fetchID(@NotNull final UUID id );
}
