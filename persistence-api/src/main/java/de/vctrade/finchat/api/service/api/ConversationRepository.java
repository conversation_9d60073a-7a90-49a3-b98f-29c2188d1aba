package de.vctrade.finchat.api.service.api;

import de.vctrade.finchat.domain.generated.dto.ConversationResponse;
import de.vctrade.finchat.domain.generated.dto.CreateConversationRequest;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;

import java.util.List;
import java.util.UUID;

public interface ConversationRepository {

    @NotNull
    ConversationResponse save(final @NotNull CreateConversationRequest conversation);

    @NotNull List<ConversationResponse> fetchAll();

    @NotNull List<ConversationResponse> fetchByReferenceId(final @NotNull UUID referenceId);

    @Nullable
    ConversationResponse fetchID(@NotNull final UUID id );
}
