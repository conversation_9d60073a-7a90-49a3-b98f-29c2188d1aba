# Domain Model Design: Java Records vs Lombok POJOs

## Table of Contents
1. [Architecture Overview](#architecture-overview)
2. [Decision Criteria](#decision-criteria)
3. [When to Use Java Records](#when-to-use-java-records)
4. [When to Use Lombok POJOs](#when-to-use-lombok-pojjos)
5. [Implementation Examples](#implementation-examples)
6. [Framework Integration Considerations](#framework-integration-considerations)
7. [Migration Guidelines](#migration-guidelines)
8. [Best Practices and Anti-Patterns](#best-practices-and-anti-patterns)
9. [Future Considerations](#future-considerations)

## Architecture Overview

This codebase follows a layered architecture with clear separation between different types of data structures:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Layer     │    │  Service Layer  │    │ Persistence     │
│   (DTOs)        │    │  (Domain)       │    │ (Entities)      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ Regular POJOs   │    │ Mixed: Records  │    │ Lombok POJOs    │
│ (Generated)     │    │ & Lombok POJOs  │    │ with @Data      │
│ Jackson         │    │ Domain Models   │    │ MongoDB/JPA     │
│ Serializable    │    │ Value Objects   │    │ Validation      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Current Technology Stack
- **Spring Boot 3.1.3** with Java 17
- **MongoDB** via Spring Data MongoDB 4.1.3
- **Lombok 1.18.28** for boilerplate reduction
- **Jakarta Validation** for bean validation
- **MapStruct 1.5.3** for object mapping
- **Jackson** for JSON serialization

## Decision Criteria

| Criteria | Java Records | Lombok POJOs | Regular POJOs |
|----------|-------------|--------------|---------------|
| **Immutability** | ✅ Built-in | ✅ With `@FieldDefaults(makeFinal=true)` | ❌ Manual |
| **Boilerplate** | ✅ Minimal | ✅ Annotations only | ❌ Verbose |
| **Framework Integration** | ⚠️ Limited | ✅ Full support | ✅ Full support |
| **Performance** | ✅ Optimized | ✅ Good | ✅ Standard |
| **Debugging** | ✅ Clear toString | ✅ Generated toString | ❌ Object@hash |
| **Thread Safety** | ✅ Immutable | ✅ If final fields | ❌ Depends |
| **JPA/MongoDB** | ❌ No default constructor | ✅ Full support | ✅ Full support |
| **Jackson Serialization** | ⚠️ Limited control | ✅ Full control | ✅ Full control |
| **Builder Pattern** | ❌ No builders | ✅ @Builder | ❌ Manual |
| **Inheritance** | ❌ No extends | ✅ Full support | ✅ Full support |
| **Validation** | ⚠️ Constructor only | ✅ Field-level | ✅ Field-level |

## When to Use Java Records

### ✅ Use Records For:

1. **Pure Value Objects** - Simple data carriers with no behavior
2. **Immutable Data** - Data that should never change after creation
3. **Simple Aggregation** - Grouping related primitive values
4. **Configuration Objects** - Application settings and parameters
5. **Service Layer DTOs** - Internal data transfer between services

### 📋 Record Characteristics:
- Immutable by default
- Automatic equals(), hashCode(), toString()
- Component accessor methods
- Compact constructor syntax
- No inheritance support
- No mutable state

### 🎯 Perfect Use Cases:
```java
// Content metadata (value object)
public record ContentMetadata(String hash, Long size, Boolean inlinedContent) {}

// API responses for simple data
public record DocumentSummary(UUID id, String filename, Long size) {}

// Configuration objects
public record DatabaseConfig(String host, int port, String database) {}

// Service-to-service communication
public record ProcessingResult(boolean success, String message, long processingTime) {}
```

## When to Use Lombok POJOs

### ✅ Use Lombok POJOs For:

1. **Domain Entities** - Core business objects with identity
2. **Persistence Entities** - Database-mapped objects
3. **Complex Business Objects** - Objects with behavior and state
4. **Objects requiring JPA/MongoDB** - Framework integration needed
5. **Mutable Objects** - Objects that change over time
6. **Objects with Inheritance** - Complex hierarchies

### 📋 Lombok POJO Characteristics:
- Flexible mutability control
- Full framework integration
- Builder pattern support
- Inheritance support
- Advanced annotation options
- Fine-grained control

### 🎯 Perfect Use Cases:
```java
// Domain entities with complex state
@Getter
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Document { /* complex business object */ }

// Persistence entities
@Data
@SuperBuilder
@Document("documents")
public class DocumentEntity extends BaseEntity { /* database mapping */ }

// Service layer objects with behavior
@Data
@Builder
public class TextChunk { /* processing object */ }
```

## Implementation Examples

### Example 1: ContentMetadata as Java Record (Value Object)

**✅ Current Implementation:**
```java
package de.vctrade.finchat.domain.model;

public record ContentMetadata(
    String hash,
    Long size,
    Boolean inlinedContent
) {
}
```

**Why Record is Perfect Here:**
- ✅ Simple value aggregation
- ✅ Immutable by nature
- ✅ No framework dependencies
- ✅ Clear equality semantics
- ✅ Thread-safe
- ✅ Minimal memory footprint

### Example 2: Document as Optimized Lombok POJO (Domain Entity)

**✅ Current Implementation:**
```java
package de.vctrade.finchat.domain.model;

@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class Document {

    private UUID id;
    private UUID externalDocumentId;
    private String filename;
    private ContentMetadata contentMetadata;  // Composition with record
    private String contentType;
    private LocalDateTime uploadedAt;
    private UUID collectionId;
    private UUID indexingJobId;
    private IndexingStage indexingStage;
    private UUID rootId;
    private UUID parentId;
    private DocumentNodeType nodeType;
    private String content;
    private Map<String, Object> metadata;
}
```

**Why Lombok POJO is Perfect Here:**
- ✅ Complex domain entity with multiple relationships
- ✅ Builder pattern for flexible construction
- ✅ Framework compatibility (could be JPA/MongoDB)
- ✅ Immutable through @FieldDefaults
- ✅ Contains other value objects (ContentMetadata)
- ✅ Business identity through UUID

### Example 3: IngestionJob as Optimized Lombok POJO (Domain Entity)

**✅ Current Implementation:**
```java
package de.vctrade.finchat.domain.model;

@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IngestionJob {

    private UUID id;
    private UUID collectionId;
    private UUID documentId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private IndexingJobStatus status;
    private IndexingStage indexingStage;
    private String error;
}
```

**Why Lombok POJO is Perfect Here:**
- ✅ Domain entity with business identity
- ✅ Temporal state tracking (created/updated)
- ✅ Mutable status for state transitions
- ✅ Error handling capabilities
- ✅ Framework integration ready

### Example 4: DocumentEntity as MongoDB Entity

**✅ Current Implementation:**
```java
package de.vctrade.finchat.model.entity;

@Document("documents")
@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class DocumentEntity extends BaseEntity {

    @NotNull
    private String externalId;

    @NotNull
    @Size(max = 255)
    private String name;

    @Size(max = 1000)
    private String description;

    // ... more fields with validation
}
```

**Why @Data is Perfect Here:**
- ✅ Full Spring Data MongoDB support
- ✅ Jakarta validation integration
- ✅ Inheritance from BaseEntity
- ✅ Field name constants for queries
- ✅ Mutable for ORM operations

### Example 5: Generated DTOs as Regular POJOs

**✅ Current Implementation:**
```java
package de.vctrade.finchat.domain.generated.dto;

@Schema(name = "DocumentResponse", description = "Response model for document details.")
public class DocumentResponse implements Serializable {

    // Full getters/setters implementation
    // Manual equals/hashCode/toString
    // Jackson annotations
    // OpenAPI annotations
}
```

**Why Regular POJO is Perfect Here:**
- ✅ Generated from OpenAPI specification
- ✅ Full Jackson customization control
- ✅ API contract compliance
- ✅ Serializable for caching
- ✅ Framework agnostic

## Framework Integration Considerations

### Spring Data MongoDB Integration

**✅ Lombok POJOs with MongoDB:**
```java
@Document("documents")
@Data
@SuperBuilder
@FieldNameConstants
public class DocumentEntity extends BaseEntity {
    // Full Spring Data MongoDB support
    // Query by example works
    // Custom repository methods supported
    // Validation integration works
}
```

**❌ Records with MongoDB:**
```java
// This WON'T work with Spring Data MongoDB
@Document("documents")
public record DocumentRecord(UUID id, String name) {
    // No default constructor - JPA/MongoDB fails
    // No setter methods - framework can't populate
    // Limited validation support
}
```

### Jackson Serialization

**✅ Records with Jackson (Simple Cases):**
```java
// Works well for API responses
public record ApiResponse(String status, String message) {}

// Limited customization options
// No @JsonProperty on constructor parameters
// No fine-grained control
```

**✅ Lombok POJOs with Jackson (Full Control):**
```java
@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class ComplexResponse {
    @JsonProperty("external_id")
    private String externalId;

    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime timestamp;

    @JsonIgnore
    private String internalField;
}
```

### Validation Integration

**⚠️ Records with Validation (Limited):**
```java
public record ValidatedRecord(
    @NotNull @Size(max = 100) String name,
    @Min(1) @Max(1000) Integer value
) {
    // Validation only in constructor
    // No field-level validation
    // No conditional validation
}
```

**✅ Lombok POJOs with Validation (Full Support):**
```java
@Data
@Builder
public class ValidatedEntity {
    @NotNull
    @Size(max = 255)
    private String name;

    @Min(1)
    @Max(1000)
    private Integer value;

    @Valid
    @NotNull
    private List<@Valid SubObject> children;
}
```

## Migration Guidelines

### Phase 1: Assessment (✅ Complete)
- [x] Identify all domain model classes
- [x] Categorize by usage pattern (value object vs entity)
- [x] Document current implementation approaches
- [x] Identify framework integration points

### Phase 2: Standardization Rules

**✅ Keep as Records:**
- Pure value objects without behavior
- Simple data aggregations (< 5 fields)
- Configuration objects
- Internal service DTOs
- Objects without framework dependencies

**✅ Keep as Lombok POJOs:**
- Domain entities with identity
- Persistence entities (MongoDB/JPA)
- Objects requiring validation
- Complex business objects
- Objects with inheritance
- Mutable state objects

**✅ Keep as Regular POJOs:**
- Generated API DTOs
- Framework-specific requirements
- Legacy compatibility needs
- Special serialization requirements

### Phase 3: Future Development Standards

**For New Classes, Use This Decision Tree:**

```
Is it a simple value aggregation (< 5 fields)?
├─ YES → Is it immutable data without behavior?
│   ├─ YES → Does it need framework integration?
│   │   ├─ NO → ✅ USE RECORD
│   │   └─ YES → ❓ Evaluate specific needs
│   └─ NO → 🔄 Consider Lombok POJO
└─ NO → Is it a domain entity or complex object?
    ├─ YES → ✅ USE LOMBOK POJO
    └─ NO → Is it generated or API contract?
        ├─ YES → ✅ USE REGULAR POJO
        └─ NO → 🔄 Re-evaluate requirements
```

### Phase 4: Refactoring Guidelines

**Safe Refactoring (Low Risk):**
1. Internal service DTOs → Records
2. Simple configuration objects → Records
3. Complex POJOs → Lombok POJOs (if not already)

**Risky Refactoring (High Impact):**
1. Persistence entities (requires testing)
2. API DTOs (breaking changes)
3. Objects with complex validation

## Best Practices and Anti-Patterns

### ✅ Best Practices

#### Record Best Practices:
```java
// ✅ Good: Simple, focused value object
public record DocumentMetadata(String hash, Long size, String contentType) {}

// ✅ Good: Clear validation in constructor
public record ValidatedConfig(String host, int port) {
    public ValidatedConfig {
        if (port < 1 || port > 65535) {
            throw new IllegalArgumentException("Invalid port: " + port);
        }
    }
}

// ✅ Good: Composition with other records
public record DocumentSummary(UUID id, ContentMetadata metadata, LocalDateTime created) {}
```

#### Lombok POJO Best Practices:
```java
// ✅ Good: Immutable domain entity
@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class ImmutableDocument {
    UUID id;
    String filename;
    ContentMetadata metadata;
}

// ✅ Good: Mutable entity with controlled access
@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MutableJob {
    UUID id;
    @Setter JobStatus status;  // Only status is mutable
    LocalDateTime createdAt;
}
```

### ❌ Anti-Patterns

#### Record Anti-Patterns:
```java
// ❌ Bad: Too many fields (should be Lombok POJO)
public record OverlyComplexRecord(
    UUID id, String name, String description, LocalDateTime created,
    LocalDateTime updated, String status, String error, Map<String, Object> metadata,
    List<String> tags, UUID parentId, UUID rootId, String contentType
) {}

// ❌ Bad: Mutable collections (breaks immutability)
public record MutableRecord(List<String> items) {
    // items.add() mutates the record!
}

// ❌ Bad: Using records for entities needing JPA
@Entity
public record JpaRecord(Long id, String name) {
    // Won't work with JPA/Hibernate
}
```

#### Lombok Anti-Patterns:
```java
// ❌ Bad: @Data on immutable objects (unnecessary setters)
@Data
@Builder
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class OverAnnotatedValue {
    String value;  // Should be a record instead
}

// ❌ Bad: Missing @FieldDefaults for intended immutability
@Getter
@Builder
public class UnintentionallyMutable {
    private String value;  // Can be modified via reflection
}
```

### 🔧 Code Review Checklist

**For Records:**
- [ ] Is it truly a value object with no behavior?
- [ ] Are all fields immutable types or defensive copies?
- [ ] Is validation appropriate for constructor?
- [ ] No framework integration requirements?
- [ ] Fewer than 6 fields total?

**For Lombok POJOs:**
- [ ] Correct annotation combination for use case?
- [ ] Proper access control with @FieldDefaults?
- [ ] Builder pattern when construction is complex?
- [ ] Appropriate mutability for the domain concept?
- [ ] Framework annotations (MongoDB/JPA) when needed?

## Future Considerations

### Java Language Evolution

**Upcoming Java Features:**
- **Pattern Matching:** Enhanced switch expressions with records
- **Data Classes:** Potential Java enhancement for mutable data classes
- **Value Classes:** Project Valhalla improvements for value types

### Framework Evolution

**Spring Framework:**
- Enhanced record support in Spring Data
- Better record integration with validation
- Native compilation improvements (GraalVM)

**MongoDB/JPA:**
- Potential native record support
- Improved immutable entity patterns
- Better integration with functional programming

### Migration Strategy for Future

**When Java Adds Better Data Class Support:**
1. Evaluate new language features
2. Update coding standards
3. Gradual migration of appropriate classes
4. Maintain backward compatibility

**Performance Considerations:**
- Monitor memory usage patterns
- Benchmark serialization performance
- Consider GraalVM native image compatibility
- Evaluate startup time impacts

### Tooling Evolution

**Development Tools:**
- IDE support for records and Lombok
- Static analysis rule updates
- Code generation improvements
- Refactoring tool enhancements

### Decision Review Cadence

**Quarterly Reviews:**
- Assess new Java language features
- Review framework update impacts
- Evaluate developer experience feedback
- Update guidelines based on learnings

**Annual Architecture Review:**
- Comprehensive pattern assessment
- Performance impact analysis
- Developer productivity metrics
- Strategic technology decisions

---

## Summary

This architecture follows a **pragmatic approach** to data class design:

1. **Records for Value Objects** - Simple, immutable data aggregations
2. **Lombok POJOs for Entities** - Complex domain objects with framework needs
3. **Regular POJOs for Generated Code** - API contracts and legacy compatibility

The key is choosing the right tool for the specific use case while maintaining consistency across the codebase. This approach maximizes developer productivity while ensuring robust, maintainable code that integrates well with the Spring Boot ecosystem.

**Next Steps:**
1. Apply these guidelines to new development
2. Gradually refactor appropriate existing classes
3. Update team coding standards
4. Monitor and refine based on practical experience
