package de.vctrade.finchat.ingest.domain.service;

import de.vctrade.finchat.ingest.domain.model.extraction.ExtractionResult;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Mono;

import java.io.InputStream;

/**
 * Service for extracting text content from various document formats.
 * Supports PDF, DOCX, TXT, and other common document types.
 */
public interface TextExtractionService {

    /**
     * Extracts text and metadata from a document input stream.
     *
     * @param inputStream the document input stream
     * @param contentType the MIME type of the document (e.g., "application/pdf")
     * @param filename    the original filename for context and metadata
     * @return a Mono containing the extraction result with text and metadata
     */
    @NotNull
    Mono<ExtractionResult> extractText(
            @NotNull InputStream inputStream,
            @NotNull String contentType,
            @NotNull String filename
    );

    /**
     * Checks if the given content type is supported for text extraction.
     *
     * @param contentType the MIME type to check
     * @return true if the content type is supported, false otherwise
     */
    boolean supportsContentType(@NotNull String contentType);
}
