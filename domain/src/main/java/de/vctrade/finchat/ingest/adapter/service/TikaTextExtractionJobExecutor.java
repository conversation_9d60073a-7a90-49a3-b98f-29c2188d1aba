package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.model.document.DocumentAggregate;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.repository.DocumentRepository;
import de.vctrade.finchat.ingest.domain.model.extraction.ExtractionResult;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import de.vctrade.finchat.ingest.domain.service.JobExecutor;
import de.vctrade.finchat.ingest.domain.service.TextExtractionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * Job executor for text extraction using Apache Tika.
 * Handles document text extraction and stores results.
 */
@Slf4j
@AllArgsConstructor
@Component
public class TikaTextExtractionJobExecutor implements JobExecutor {

    private final TextExtractionService textExtractionService;
    private final DocumentRepository documentRepository;
    private final IngestionJobService ingestionJobService;

    private final ConcurrentHashMap<UUID, AtomicBoolean> cancellationFlags = new ConcurrentHashMap<>();

    @Override
    public Mono<Void> execute(UUID jobId) {
        cancellationFlags.put(jobId, new AtomicBoolean(false));

        return ingestionJobService.getJob(jobId)
            .flatMap(job -> {
                if (job == null) {
                    log.error("Job {} not found", jobId);
                    return Mono.error(new IllegalArgumentException("Job not found: " + jobId));
                }

                log.info("Starting text extraction for job {} (document {})", jobId, job.getDocumentId());

                return processDocument(jobId, job.getDocumentId());
            })
            .doFinally(signalType -> cancellationFlags.remove(jobId));
    }

    private Mono<Void> processDocument(UUID jobId, UUID documentId) {
        return documentRepository.fetchById(documentId)
            .flatMap(document -> {
                if (document == null) {
                    return Mono.error(new IllegalArgumentException("Document not found: " + documentId));
                }

                return extractTextFromDocument(jobId, document);
            })
            .flatMap(extractionResult ->
                    storeExtractionResult(jobId, documentId, extractionResult)
            );
    }

    private Mono<ExtractionResult> extractTextFromDocument(UUID jobId, DocumentAggregate document) {
        return ingestionJobService
                .updateIndexingStage(jobId, IndexingStage.EXTRACTING)
                .then(Mono.fromCallable(() -> {
            checkCancellation(jobId);

            log.debug("Extracting text from document: {} ({})",
                    document.getDocumentMetadata().filename(),
                    document.getContentMetadata().contentType());

            // FIXME
            // byte[] content = document.getContent();
            byte[] content = ByteBuffer.allocate(0).array();

            if (content == null || content.length == 0) {
                throw new IllegalArgumentException("Document content is empty");
            }

            try (InputStream inputStream = new ByteArrayInputStream(content)) {
                ExtractionResult result = textExtractionService.extractText(
                        inputStream,
                        document.getContentMetadata().contentType(),
                        document.getDocumentMetadata().filename()
                ).block();

                checkCancellation(jobId);

                log.info("Text extraction completed for document {}: {} characters extracted",
                    document.getId(), result.getText().length());

                return result;
            }
        }));
    }

    private Mono<Void> storeExtractionResult(UUID jobId, UUID documentId, ExtractionResult extractionResult) {
        checkCancellation(jobId);

        log.debug("Storing extraction result for document {}", documentId);

        // Update progress to 75% - storing results
        return ingestionJobService.updateIndexingStage(jobId, IndexingStage.EXTRACTING)
                .then(Mono.fromRunnable(() -> {
                    // TODO: Store the extracted text and metadata in the document
                    // This would typically involve:
                    // 1. Updating the document with extracted text
                    // 2. Storing text chunks for vector search
                    // 3. Storing metadata for search/filtering
                    // For now, we'll just log the results

                    log.info("Extracted text length: {}", extractionResult.getText().length());
                    log.info("Number of chunks: {}", extractionResult.getChunks().size());
                    log.info("Metadata: {}", extractionResult.getMetadata());

                    checkCancellation(jobId);
                }))
                .then(ingestionJobService.updateIndexingStage(jobId, IndexingStage.EXTRACTING));
    }

    private void checkCancellation(UUID jobId) {
        AtomicBoolean cancelled = cancellationFlags.get(jobId);
        if (cancelled != null && cancelled.get()) {
            log.info("Job {} was cancelled, stopping execution", jobId);
            throw new RuntimeException("Job cancelled");
        }
    }

    @Override
    public String getJobType() {
        return "TEXT_EXTRACTION";
    }

    @Override
    public boolean isCancellable() {
        return true;
    }

    @Override
    public Mono<Void> cancel(UUID jobId) {
        return Mono.fromRunnable(() -> {
            AtomicBoolean cancelled = cancellationFlags.get(jobId);
            if (cancelled != null) {
                cancelled.set(true);
                log.info("Cancellation requested for job {}", jobId);
            }
        });
    }
}
