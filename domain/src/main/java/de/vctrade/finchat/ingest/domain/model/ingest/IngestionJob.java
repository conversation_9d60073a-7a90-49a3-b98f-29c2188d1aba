package de.vctrade.finchat.ingest.domain.model.ingest;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.UUID;

@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class IngestionJob {

    private UUID id;

    private UUID collectionId;

    private UUID documentId;

    private int retryCount;

    private Instant createdAt;

    private Instant updatedAt;

    private IndexingStage indexingStage;

    private String error;

}
