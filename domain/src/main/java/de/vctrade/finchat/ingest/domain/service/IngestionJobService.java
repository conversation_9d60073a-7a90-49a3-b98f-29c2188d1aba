package de.vctrade.finchat.ingest.domain.service;

import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Service interface for document ingestion job business logic.
 * Manages job lifecycle, status tracking, and retry logic.
 */
public interface IngestionJobService {

    /**
     * Start a new document ingestion job
     * @param documentId the document to ingest
     */
    Mono<IngestionJob> startIngestionJob(UUID documentId);

    /**
     * Update job status and progress
     * @param jobId the job to update
     * @param indexingStage indexing stage
     */
    Mono<Void> updateIndexingStage(UUID jobId, IndexingStage indexingStage);

    /**
     * Mark job as failed with error message
     * @param jobId the job that failed
     * @param errorMessage error description
     */
    Mono<Void> markJobFailed(UUID jobId, String errorMessage);

    /**
     * Mark job as completed successfully
     * @param jobId the completed job
     */
    Mono<Void> markJobCompleted(UUID jobId);

    /**
     * Retry a failed job
     * @param jobId the job to retry
     */
    Mono<Void> retryJob(UUID jobId);

    /**
     * Get job by ID
     * @param jobId the job ID
     * @return Mono with job entity or empty if not found
     */
    Mono<IngestionJob> getJob(UUID jobId);

    /**
     * Cancel a running job
     * @param jobId the job to cancel
     */
    Mono<Void> cancelJob(UUID jobId);
}
