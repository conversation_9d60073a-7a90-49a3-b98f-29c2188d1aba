package de.vctrade.finchat.ingest.application.example.job;

import de.vctrade.finchat.ingest.application.handler.DocumentIngestionFacade;
import de.vctrade.finchat.model.entity.IngestionJobEntity;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.UUID;

/**
 * Example usage of the document ingestion job system.
 * Demonstrates typical workflows and best practices.
 */
@Slf4j
@AllArgsConstructor
@Component
public class JobSystemUsageExample {

    private final DocumentIngestionFacade ingestionFacade;

    /**
     * Example: Process a batch of documents with monitoring
     */
    public Mono<Void> processBatchOfDocuments(List<UUID> documentIds) {
        log.info("Starting batch processing of {} documents", documentIds.size());

        return Flux.fromIterable(documentIds)
            .flatMap(documentId -> {
                log.debug("Starting ingestion for document {}", documentId);

                return ingestionFacade.startDocumentIngestion(documentId)
                    .doOnSuccess(job -> log.info("Started job {} for document {}",
                        job.getId(), documentId))
                    .doOnError(error -> log.error("Failed to start job for document {}: {}",
                        documentId, error.getMessage()));
            })
            .then()
            .doOnSuccess(v -> log.info("Batch processing initiated successfully"))
            .doOnError(error -> log.error("Batch processing failed: {}", error.getMessage()));
    }

    /**
     * Example: Monitor job progress with polling
     */
    public Mono<IngestionJobEntity> monitorJobUntilCompletion(UUID jobId) {
        return Mono.defer(() -> ingestionFacade.getJobStatus(jobId))
            .flatMap(job -> {
                if (job == null) {
                    return Mono.error(new IllegalArgumentException("Job not found: " + jobId));
                }

                log.debug("Job {} status: {} ({}% complete)",
                    jobId, job.getStatus(), job.getProgress());

                switch (job.getStatus()) {
                    case COMPLETED:
                        log.info("Job {} completed successfully", jobId);
                        return Mono.just(job);
                    case FAILED:
                        log.warn("Job {} failed: {}", jobId, job.getErrorMessage());
                        return Mono.just(job);
                    case CANCELLED:
                        log.info("Job {} was cancelled", jobId);
                        return Mono.just(job);
                    default:
                        // Still running, check again in 1 second
                        return Mono.delay(java.time.Duration.ofSeconds(1))
                            .then(monitorJobUntilCompletion(jobId));
                }
            });
    }

    /**
     * Example: Process document with automatic retry on failure
     */
    public Mono<IngestionJobEntity> processDocumentWithRetry(UUID documentId, int maxAttempts) {
        return processDocumentAttempt(documentId, 1, maxAttempts);
    }

    private Mono<IngestionJobEntity> processDocumentAttempt(UUID documentId, int attempt, int maxAttempts) {
        log.info("Processing document {} (attempt {}/{})", documentId, attempt, maxAttempts);

        return ingestionFacade.startDocumentIngestion(documentId)
            .flatMap(job -> monitorJobUntilCompletion(job.getId()))
            .flatMap(job -> {
                if (job.getStatus() == IngestionJobEntity.IngestionJobStatus.COMPLETED) {
                    return Mono.just(job);
                } else if (attempt < maxAttempts) {
                    log.warn("Document {} processing failed, retrying...", documentId);
                    return Mono.delay(java.time.Duration.ofSeconds(5))
                        .then(processDocumentAttempt(documentId, attempt + 1, maxAttempts));
                } else {
                    log.error("Document {} processing failed after {} attempts", documentId, maxAttempts);
                    return Mono.just(job);
                }
            });
    }

    /**
     * Example: Get system status and health check
     */
    public Mono<String> getSystemHealthReport() {
        return ingestionFacade.getSystemStatus()
            .map(status -> String.format(
                "Job System Health Report:\n" +
                "- Active jobs: %d\n" +
                "- System healthy: %s\n" +
                "- Timestamp: %s",
                status.getActiveJobCount(),
                status.isHealthy() ? "YES" : "NO",
                java.time.LocalDateTime.now()
            ))
            .doOnSuccess(report -> log.info("System status: {}", report));
    }

    /**
     * Example: Emergency shutdown with job cancellation
     */
    public Mono<Void> emergencyShutdown(List<UUID> activeJobIds) {
        log.warn("Emergency shutdown requested, cancelling {} active jobs", activeJobIds.size());

        return Flux.fromIterable(activeJobIds)
            .flatMap(jobId -> {
                log.info("Cancelling job {}", jobId);
                return ingestionFacade.cancelIngestionJob(jobId)
                    .doOnSuccess(cancelled -> {
                        if (cancelled) {
                            log.info("Job {} cancelled successfully", jobId);
                        } else {
                            log.warn("Failed to cancel job {}", jobId);
                        }
                    });
            })
            .then()
            .doOnSuccess(v -> log.info("Emergency shutdown completed"));
    }
}
