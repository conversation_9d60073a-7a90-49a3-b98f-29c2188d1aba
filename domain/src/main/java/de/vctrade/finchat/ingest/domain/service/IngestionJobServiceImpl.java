package de.vctrade.finchat.ingest.domain.service;

import de.vctrade.finchat.ingest.domain.model.ingest.CreateIngestionJobData;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.ingest.domain.repository.IngestionJobRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Objects;
import java.util.UUID;

/**
 * Implementation of ingestion job service for managing document ingestion job lifecycle.
 */
@Slf4j
@Service
public class IngestionJobServiceImpl implements IngestionJobService {

    private final IngestionJobRepository ingestionJobRepository;

    private final int maxRetryCount;

    public IngestionJobServiceImpl(
            IngestionJobRepository ingestionJobRepository,
            @Value("${vctrade.module.ingestion.retry-count:${finchat.job.max-retry-count:3}}")
            int maxRetryCount
    ) {
        this.ingestionJobRepository = ingestionJobRepository;
        this.maxRetryCount = maxRetryCount;
    }

    @Override
    public Mono<IngestionJob> startIngestionJob(UUID documentId) {
        return Mono.fromCallable(() -> {
            log.debug("Starting ingestion job for document {}", documentId);

            final CreateIngestionJobData request = new CreateIngestionJobData(documentId);
            final IngestionJob job = ingestionJobRepository.create(request);

            log.info("Created ingestion job {} for document {}", job.getId(), documentId);

            return job;
        });
    }

    @Override
    public Mono<Void> updateIndexingStage(UUID jobId, IndexingStage indexingStage) {
        return Mono.fromCallable(() -> {
            log.debug("Updating job {} status to {}", jobId, indexingStage);

            ingestionJobRepository.updateStage(jobId, indexingStage, null);
        });
    }

    @Override
    public Mono<Void> markJobFailed(UUID jobId, String errorMessage) {
        return Mono.fromCallable(() -> {
            log.warn("Marking job {} as failed: {}", jobId, errorMessage);

            ingestionJobRepository.updateStage(
                jobId,
                IndexingStage.FAILED,
                errorMessage
            );
        });
    }

    @Override
    public Mono<IngestionJob> markJobCompleted(UUID jobId) {
        return Mono.fromCallable(() -> {
            log.info("Marking job {} as completed", jobId);

            IngestionJob job = ingestionJobRepository.updateStage(
                jobId,
                IndexingStage.COMPLETED,
                null
            );

            return mapToEntity(job);
        });
    }

    @Override
    public Mono<Void> retryJob(UUID jobId) {
        return getJob(jobId)
            .flatMap(job -> {
                if (job.getRetryCount() >= maxRetryCount) {
                    log.warn("Job {} has reached max retry count ({}), not retrying", jobId, maxRetryCount);
                    return Mono.empty();
                }

                log.info("Retrying job {} (attempt {})", jobId, job.getRetryCount() + 1);

                return Mono.fromCallable(() -> ingestionJobRepository.markForRetry(jobId));
            });
    }

    @Override
    public Mono<IngestionJob> getJob(UUID jobId) {
        return Mono.fromCallable(() -> ingestionJobRepository.fetchById(jobId))
                .filter(Objects::nonNull);
    }

    @Override
    public Mono<Void> cancelJob(UUID jobId) {
        return Mono.fromRunnable(() -> {
            log.info("Cancelling job {}", jobId);
            ingestionJobRepository.updateStage(
                jobId,
                IndexingStage.CANCELLED,
                "Job cancelled by user"
            );
        });
    }

}
