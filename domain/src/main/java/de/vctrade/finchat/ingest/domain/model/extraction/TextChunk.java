package de.vctrade.finchat.ingest.domain.model.extraction;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * Represents a chunk of text with positional metadata for structured processing.
 */
@Data
@Builder
public class TextChunk {

    /**
     * The text content of this chunk.
     */
    @NotNull
    private final String content;

    /**
     * The page number where this chunk appears (1-based, 0 if not applicable).
     */
    private final int pageNumber;

    /**
     * The section or heading this chunk belongs to (if applicable).
     */
    private final String section;

    /**
     * The character offset of this chunk in the full document.
     */
    private final int startOffset;

    /**
     * The length of this chunk in characters.
     */
    private final int length;
}
