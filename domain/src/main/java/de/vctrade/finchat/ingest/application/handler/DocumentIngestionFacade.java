package de.vctrade.finchat.ingest.application.handler;

import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import de.vctrade.finchat.ingest.domain.service.JobOrchestrator;
import de.vctrade.finchat.ingest.adapter.service.TikaTextExtractionJobExecutor;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Facade service for document ingestion that coordinates between
 * job orchestration, business logic, and execution.
 */
@Slf4j
@AllArgsConstructor
@Service
public class DocumentIngestionFacade {

    private final JobOrchestrator jobOrchestrator;
    private final IngestionJobService ingestionJobService;
    private final TikaTextExtractionJobExecutor textExtractionExecutor;

    /**
     * Start a new document ingestion job for the given document
     * @param documentId the document to process
     * @return Mono with the created job entity
     */
    public Mono<IngestionJob> startDocumentIngestion(UUID documentId) {
        log.info("Starting document ingestion for document {}", documentId);

        return ingestionJobService.startIngestionJob(documentId)
            .flatMap(job ->
                    jobOrchestrator.submitJob(job.getId(), textExtractionExecutor)
                            .thenReturn(job)
            )
            .doOnSuccess(job ->
                    log.info("Document ingestion job {} submitted successfully", job.getId())
            )
            .doOnError(error ->
                    log.error("Failed to start document ingestion for document {}: {}",
                            documentId, error.getMessage())
            );
    }

    /**
     * Cancel an active ingestion job
     * @param jobId the job to cancel
     * @return Mono that completes when cancellation is processed
     */
    public Mono<Boolean> cancelIngestionJob(UUID jobId) {
        log.info("Cancelling ingestion job {}", jobId);

        return jobOrchestrator.cancelJob(jobId)
            .flatMap(cancelled -> {
                if (cancelled) {
                    return ingestionJobService.cancelJob(jobId).thenReturn(true);
                }
                return Mono.just(false);
            })
            .doOnSuccess(cancelled -> {
                if (cancelled) {
                    log.info("Ingestion job {} cancelled successfully", jobId);
                } else {
                    log.warn("Failed to cancel ingestion job {}", jobId);
                }
            });
    }

    /**
     * Retry a failed ingestion job
     * @param jobId the job to retry
     * @return Mono with updated job entity, or empty if retry not possible
     */
    public Mono<Void> retryIngestionJob(UUID jobId) {
        log.info("Retrying ingestion job {}", jobId);

        return ingestionJobService.retryJob(jobId)
                .then(jobOrchestrator.submitJob(jobId, textExtractionExecutor))
                .doOnSuccess(v -> {
                    log.info("Ingestion job {} retry submitted successfully", jobId);
                }).doOnError(ex -> {
                    log.warn("Cannot retry ingestion job, max retries exceeded", ex);
                });
    }

    /**
     * Get the status of an ingestion job
     * @param jobId the job ID
     * @return Mono with job entity or empty if not found
     */
    public Mono<IngestionJob> getJobStatus(UUID jobId) {
        return ingestionJobService.getJob(jobId);
    }

    /**
     * Get current system status
     * @return information about active jobs and system health
     */
    public Mono<JobSystemStatus> getSystemStatus() {
        return Mono.fromCallable(() -> {
            int activeJobs = jobOrchestrator.getActiveJobCount();

            return JobSystemStatus.builder()
                .activeJobCount(activeJobs)
                .healthy(true)
                .build();
        });
    }

    /**
     * Data class for system status information
     */
    public static class JobSystemStatus {
        private final int activeJobCount;
        private final boolean healthy;

        private JobSystemStatus(int activeJobCount, boolean healthy) {
            this.activeJobCount = activeJobCount;
            this.healthy = healthy;
        }

        public static Builder builder() {
            return new Builder();
        }

        public int getActiveJobCount() {
            return activeJobCount;
        }

        public boolean isHealthy() {
            return healthy;
        }

        public static class Builder {
            private int activeJobCount;
            private boolean healthy;

            public Builder activeJobCount(int activeJobCount) {
                this.activeJobCount = activeJobCount;
                return this;
            }

            public Builder healthy(boolean healthy) {
                this.healthy = healthy;
                return this;
            }

            public JobSystemStatus build() {
                return new JobSystemStatus(activeJobCount, healthy);
            }
        }
    }
}
