package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Response model for ingestion job details.
 */
@Schema(name = "IngestionJobResponse", description = "Response model for ingestion job details.")
public class IngestionJobResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("id")
    private UUID id;

    @JsonProperty("documentId")
    private UUID documentId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("errorMessage")
    private @Nullable String errorMessage;

    @JsonProperty("retryCount")
    private Integer retryCount;

    @JsonProperty("maxRetries")
    private Integer maxRetries;

    @JsonProperty("createdAt")
    private Long createdAt;

    @JsonProperty("updatedAt")
    private @Nullable Long updatedAt;

    @JsonProperty("startedAt")
    private @Nullable Long startedAt;

    @JsonProperty("completedAt")
    private @Nullable Long completedAt;

    public IngestionJobResponse() {
        super();
    }

    /**
     * Constructor with required parameters
     */
    public IngestionJobResponse(UUID id, UUID documentId, String status, Integer retryCount, Integer maxRetries, Long createdAt) {
        this.id = id;
        this.documentId = documentId;
        this.status = status;
        this.retryCount = retryCount;
        this.maxRetries = maxRetries;
        this.createdAt = createdAt;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public UUID getDocumentId() {
        return documentId;
    }

    public void setDocumentId(UUID documentId) {
        this.documentId = documentId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public @Nullable String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(@Nullable String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public Integer getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(Integer maxRetries) {
        this.maxRetries = maxRetries;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public @Nullable Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(@Nullable Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    public @Nullable Long getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(@Nullable Long startedAt) {
        this.startedAt = startedAt;
    }

    public @Nullable Long getCompletedAt() {
        return completedAt;
    }

    public void setCompletedAt(@Nullable Long completedAt) {
        this.completedAt = completedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IngestionJobResponse that = (IngestionJobResponse) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(documentId, that.documentId) &&
                Objects.equals(status, that.status) &&
                Objects.equals(errorMessage, that.errorMessage) &&
                Objects.equals(retryCount, that.retryCount) &&
                Objects.equals(maxRetries, that.maxRetries) &&
                Objects.equals(createdAt, that.createdAt) &&
                Objects.equals(updatedAt, that.updatedAt) &&
                Objects.equals(startedAt, that.startedAt) &&
                Objects.equals(completedAt, that.completedAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, documentId, status, errorMessage, retryCount, maxRetries, createdAt, updatedAt, startedAt, completedAt);
    }

    @Override
    public String toString() {
        return "IngestionJobResponse{" +
                "id=" + id +
                ", documentId=" + documentId +
                ", status='" + status + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", retryCount=" + retryCount +
                ", maxRetries=" + maxRetries +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", startedAt=" + startedAt +
                ", completedAt=" + completedAt +
                '}';
    }
}
