package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import de.vctrade.finchat.ingest.domain.service.JobExecutor;
import de.vctrade.finchat.ingest.domain.service.JobOrchestrator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * Thread pool-based job orchestrator implementation.
 * Uses Java's ExecutorService for async job execution with configurable thread pool size.
 */
@Slf4j
@Component
public class ThreadPoolJobOrchestrator implements JobOrchestrator {

    private final ExecutorService executorService;
    private final Map<UUID, Future<?>> runningJobs;
    private final IngestionJobService ingestionJobService;

    public ThreadPoolJobOrchestrator(
            @Value("${finchat.job.thread-pool.size:4}") int threadPoolSize,
            IngestionJobService ingestionJobService) {
        this.executorService = Executors.newFixedThreadPool(threadPoolSize);
        this.runningJobs = new ConcurrentHashMap<>();
        this.ingestionJobService = ingestionJobService;
        log.info("ThreadPoolJobOrchestrator initialized with {} threads", threadPoolSize);
    }

    @Override
    public Mono<Void> submitJob(UUID jobId, JobExecutor executor) {
        return Mono.fromCallable(() -> {
            log.debug("Submitting job {} for execution", jobId);

            Future<?> future = executorService.submit(() -> {
                try {
                    ingestionJobService.updateIndexingStage(jobId,
                        de.vctrade.finchat.model.entity.IngestionJobEntity.IngestionJobStatus.RUNNING, 0)
                        .then(executor.execute(jobId))
                        .then(ingestionJobService.markJobCompleted(jobId))
                        .doOnSuccess(result -> log.info("Job {} completed successfully", jobId))
                        .doOnError(error -> {
                            log.error("Job {} failed with error: {}", jobId, error.getMessage());
                            ingestionJobService.markJobFailed(jobId, error.getMessage()).subscribe();
                        })
                        .block();
                } catch (Exception e) {
                    log.error("Unexpected error in job {}: {}", jobId, e.getMessage(), e);
                    ingestionJobService.markJobFailed(jobId, "Unexpected error: " + e.getMessage()).subscribe();
                } finally {
                    runningJobs.remove(jobId);
                }
            });

            runningJobs.put(jobId, future);
            return null;
        })
        .subscribeOn(Schedulers.boundedElastic())
        .then();
    }

    @Override
    public Mono<Boolean> cancelJob(UUID jobId) {
        return Mono.fromCallable(() -> {
            Future<?> future = runningJobs.get(jobId);
            if (future != null && !future.isDone()) {
                boolean cancelled = future.cancel(true);
                if (cancelled) {
                    runningJobs.remove(jobId);
                    ingestionJobService.updateIndexingStage(jobId,
                        de.vctrade.finchat.model.entity.IngestionJobEntity.IngestionJobStatus.CANCELLED, null)
                        .subscribe();
                    log.info("Job {} cancelled successfully", jobId);
                }
                return cancelled;
            }
            return false;
        })
        .subscribeOn(Schedulers.boundedElastic());
    }

    @Override
    public int getActiveJobCount() {
        return runningJobs.size();
    }

    @Override
    public Mono<Void> shutdown() {
        return Mono.fromRunnable(() -> {
            log.info("Shutting down ThreadPoolJobOrchestrator...");
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Executor did not terminate gracefully, forcing shutdown");
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("Shutdown interrupted, forcing immediate shutdown");
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("ThreadPoolJobOrchestrator shutdown complete");
        })
        .subscribeOn(Schedulers.boundedElastic())
        .then();
    }
}
