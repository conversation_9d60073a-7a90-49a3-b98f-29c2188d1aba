package de.vctrade.finchat.ingest.domain.service;

import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * Interface for executing specific job types.
 * Implementations handle the actual work execution for different job types.
 */
public interface JobExecutor {

    /**
     * Execute the job
     * @param jobId the job identifier
     * @return Mono that completes when job execution finishes
     */
    Mono<Void> execute(UUID jobId);

    /**
     * Get the job type this executor handles
     * @return job type identifier
     */
    String getJobType();

    /**
     * Check if this executor can be cancelled
     * @return true if cancellation is supported
     */
    default boolean isCancellable() {
        return false;
    }

    /**
     * Cancel the current execution
     * @param jobId the job to cancel
     * @return Mono that completes when cancellation is processed
     */
    default Mono<Void> cancel(UUID jobId) {
        return Mono.empty();
    }
}
