package de.vctrade.finchat.ingest.adapter.api;

import de.vctrade.finchat.ingest.domain.repository.DocumentRepository;
import de.vctrade.finchat.ingest.adapter.model.CreateDocumentRequest;
import de.vctrade.finchat.ingest.adapter.model.DocumentResponse;
import de.vctrade.finchat.ingest.adapter.model.DocumentUploadRequest;
import de.vctrade.finchat.ingest.adapter.model.DocumentsResponse;
import de.vctrade.finchat.ingest.adapter.model.IngestionJobResponse;
import de.vctrade.finchat.model.entity.DocumentEntity;
import de.vctrade.finchat.ingest.domain.service.IngestionJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;
import java.util.UUID;

@Slf4j
@RestController
@RequestMapping("/documents")
@AllArgsConstructor
@Tag(name = "Document Ingestion API", description = "API for document upload and management")
public class DocumentIngestionControllerImpl implements DocumentIngestionController {

    private final DocumentRepository documentRepository;
    private final IngestionJobService ingestionJobService;

    @Operation(
            operationId = "uploadDocument",
            summary = "Upload a document",
            description = "Upload a document file or provide a URL for document ingestion",
            responses = {
                    @ApiResponse(responseCode = "201", description = "Document uploaded successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentResponse.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping(consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public Mono<ResponseEntity<DocumentResponse>> uploadDocument(
            @Parameter(description = "Document file", content = @Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE))
            @RequestPart(value = "file", required = false) MultipartFile file,
            @Parameter(description = "Document upload metadata")
            @RequestPart(value = "metadata", required = false) @Valid DocumentUploadRequest metadata
    ) {
        return Mono.fromCallable(() -> {
            try {
                if (file != null && !file.isEmpty()) {
                    // Handle file upload
                    var createRequest = new CreateDocumentRequest(
                            file.getOriginalFilename(),
                            file.getContentType(),
                            file.getSize()
                    );

                    if (metadata != null) {
                        createRequest.setExternalId(metadata.getExternalId());
                        createRequest.setCollectionId(metadata.getCollectionId());
                    }

                    var document = documentRepository.create(createRequest);

                    // Auto-process if requested
                    if (metadata == null || metadata.getAutoProcess() == null || metadata.getAutoProcess()) {
                        ingestionJobService.startIngestionJob(document.getId()).subscribe();
                    }

                    return ResponseEntity.status(HttpStatus.CREATED).body(document);

                } else if (metadata != null && metadata.getUrl() != null) {
                    // Handle URL-based document
                    var createRequest = new CreateDocumentRequest(
                            extractFilenameFromUrl(metadata.getUrl()),
                            "application/octet-stream", // Will be determined during fetch
                            0L // Will be set during fetch
                    );

                    createRequest.setExternalId(metadata.getExternalId());
                    createRequest.setCollectionId(metadata.getCollectionId());
                    createRequest.setUrl(metadata.getUrl());

                    var document = documentRepository.create(createRequest);

                    // Auto-process if requested
                    if (metadata.getAutoProcess() == null || metadata.getAutoProcess()) {
                        ingestionJobService.startIngestionJob(document.getId()).subscribe();
                    }

                    return ResponseEntity.status(HttpStatus.CREATED).body(document);

                } else {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
                }

            } catch (Exception error) {
                log.error("Failed to upload document", error);
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }

    @Operation(
            operationId = "getDocument",
            summary = "Get document details",
            description = "Retrieve details for a specific document by ID",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Document found",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/{id}")
    @Override
    public ResponseEntity<DocumentResponse> getDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        try {
            var document = documentRepository.fetchById(id);
            if (document != null) {
                return ResponseEntity.ok(document);
            }
        } catch (Exception error) {
            log.error("Cannot get document", error);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(
            operationId = "listDocuments",
            summary = "List documents",
            description = "Retrieve a list of documents with optional filtering",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Documents retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping
    @Override
    public ResponseEntity<DocumentsResponse> listDocuments(
            @Parameter(name = "collection_id", description = "Filter by collection ID", in = ParameterIn.QUERY)
            @RequestParam(value = "collection_id", required = false) UUID collectionId,
            @Parameter(name = "status", description = "Filter by indexing status", in = ParameterIn.QUERY)
            @RequestParam(value = "status", required = false) String status
    ) {
        try {
            List<DocumentResponse> documents;

            if (collectionId != null) {
                documents = documentRepository.fetchByCollectionId(collectionId);
            } else if (status != null) {
                documents = documentRepository.fetchByIndexingStatus(
                        DocumentEntity.DocumentIndexingStatus.valueOf(status.toUpperCase())
                );
            } else {
                documents = documentRepository.fetchAll();
            }

            return ResponseEntity.ok(new DocumentsResponse(documents));

        } catch (Exception error) {
            log.error("Cannot list documents", error);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @Operation(
            operationId = "deleteDocument",
            summary = "Delete a document",
            description = "Delete a document by ID",
            responses = {
                    @ApiResponse(responseCode = "204", description = "Document deleted successfully"),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public ResponseEntity<Void> deleteDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        try {
            var document = documentRepository.fetchById(id);
            if (document != null) {
                // TODO: Implement document deletion in repository
                // documentRepository.delete(id);
                log.warn("Document deletion not yet implemented for ID: {}", id);
                return ResponseEntity.noContent().build();
            }
        } catch (Exception error) {
            log.error("Cannot delete document", error);
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(
            operationId = "processDocument",
            summary = "Start document processing",
            description = "Start or restart the ingestion process for a document",
            responses = {
                    @ApiResponse(responseCode = "202", description = "Processing started",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping("/{id}/process")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    @Override
    public Mono<ResponseEntity<IngestionJobResponse>> processDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    ) {
        return Mono.fromCallable(() -> {
            try {
                var document = documentRepository.fetchById(id);
                if (document == null) {
                    return ResponseEntity.<IngestionJobResponse>notFound().build();
                }

                return ingestionJobService.startIngestionJob(id)
                        .map(jobEntity -> {
                            // Convert IngestionJobEntity to IngestionJobResponse
                            var response = new IngestionJobResponse(
                                    jobEntity.getId(),
                                    jobEntity.getDocumentId(),
                                    jobEntity.getStatus().name(),
                                    jobEntity.getRetryCount(),
                                    jobEntity.getMaxRetries(),
                                    jobEntity.getCreatedAt().toEpochMilli()
                            );
                            response.setProgress(jobEntity.getProgress());
                            response.setErrorMessage(jobEntity.getErrorMessage());
                            if (jobEntity.getUpdatedAt() != null) {
                                response.setUpdatedAt(jobEntity.getUpdatedAt().toEpochMilli());
                            }
                            if (jobEntity.getStartedAt() != null) {
                                response.setStartedAt(jobEntity.getStartedAt().toEpochMilli());
                            }
                            if (jobEntity.getCompletedAt() != null) {
                                response.setCompletedAt(jobEntity.getCompletedAt().toEpochMilli());
                            }
                            return ResponseEntity.status(HttpStatus.ACCEPTED).body(response);
                        })
                        .block();

            } catch (Exception error) {
                log.error("Cannot start document processing", error);
                return ResponseEntity.<IngestionJobResponse>status(HttpStatus.INTERNAL_SERVER_ERROR).build();
            }
        }).subscribeOn(Schedulers.boundedElastic());
    }

}
