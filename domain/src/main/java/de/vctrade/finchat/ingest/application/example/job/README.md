# Document Ingestion Job System

A clean, exchangeable background job system for document text extraction and processing.

## Architecture

### Core Interfaces

- **JobOrchestrator**: High-level job management and resource allocation
- **IngestionJobService**: Business logic for job lifecycle management
- **JobExecutor**: Specific job execution implementations

### Current Implementations

- **ThreadPoolJobOrchestrator**: Java ExecutorService-based orchestration
- **IngestionJobServiceImpl**: Full job lifecycle with retry logic and progress tracking
- **TikaTextExtractionJobExecutor**: Apache Tika-based document text extraction

### Key Features

- **Async Execution**: Non-blocking job processing with thread pools
- **Status Tracking**: Real-time job progress and status updates
- **Error Handling**: Automatic retry logic with configurable limits
- **Cancellation Support**: Graceful job cancellation
- **Progress Reporting**: Detailed progress updates during execution
- **Exchangeable Architecture**: Easy to replace orchestration layer

## Usage

### Starting a Document Ingestion Job

```java
@Autowired
private DocumentIngestionFacade ingestionFacade;

// Start processing a document
UUID documentId = UUID.randomUUID();
Mono<IngestionJobEntity> jobMono = ingestionFacade.startDocumentIngestion(documentId);

jobMono.subscribe(job -> {
    log.info("Job {} started for document {}", job.getId(), documentId);
});
```

### Monitoring Job Status

```java
UUID jobId = UUID.randomUUID();
Mono<IngestionJobEntity> statusMono = ingestionFacade.getJobStatus(jobId);

statusMono.subscribe(job -> {
    log.info("Job {} status: {} ({}% complete)", 
        jobId, job.getStatus(), job.getProgress());
});
```

### Cancelling a Job

```java
UUID jobId = UUID.randomUUID();
Mono<Boolean> cancelMono = ingestionFacade.cancelIngestionJob(jobId);

cancelMono.subscribe(cancelled -> {
    if (cancelled) {
        log.info("Job {} cancelled successfully", jobId);
    }
});
```

### Retrying a Failed Job

```java
UUID jobId = UUID.randomUUID();
Mono<IngestionJobEntity> retryMono = ingestionFacade.retryIngestionJob(jobId);

retryMono.subscribe(job -> {
    if (job != null) {
        log.info("Job {} retrying (attempt {})", jobId, job.getRetryCount());
    } else {
        log.warn("Job {} cannot be retried - max attempts reached", jobId);
    }
});
```

## Configuration

Configure via `application.yml`:

```yaml
finchat:
  job:
    thread-pool:
      size: 8                    # Number of worker threads
      queue-capacity: 100        # Job queue size
    max-retry-count: 3          # Maximum retry attempts
    retry-delay-millis: 5000    # Delay between retries
```

## Job Status Flow

```
QUEUED → RUNNING → COMPLETED
   ↓         ↓
CANCELLED  FAILED → RETRYING → RUNNING
                        ↓
                   [max retries] → FAILED
```

## Extending the System

### Adding New Job Types

1. Implement `JobExecutor` interface:
```java
@Component
public class MyCustomJobExecutor implements JobExecutor {
    @Override
    public Mono<Void> execute(UUID jobId) {
        // Your job logic here
        return Mono.empty();
    }
    
    @Override
    public String getJobType() {
        return "MY_CUSTOM_JOB";
    }
}
```

2. Use with the orchestrator:
```java
jobOrchestrator.submitJob(jobId, myCustomJobExecutor);
```

### Replacing the Orchestrator

The `ThreadPoolJobOrchestrator` can be easily replaced with:
- Spring Batch for complex workflows
- Kubernetes Jobs for distributed processing
- Message queues (RabbitMQ, Apache Kafka) for scalable processing

Simply implement the `JobOrchestrator` interface and replace the bean.

## Thread Safety

- All implementations are thread-safe
- Concurrent job execution is supported
- Status updates are atomic
- Cancellation is handled gracefully

## Error Handling

- Comprehensive exception handling in all layers
- Automatic retry with exponential backoff
- Detailed error messages stored in job entities
- Graceful degradation on system errors

## Integration with Existing Systems

The job system integrates seamlessly with:
- Existing MongoDB persistence layer via `IngestionJobRepository`
- Document processing via `TikaTextExtractionService`
- Spring Boot reactive stack with Project Reactor
- Current logging and monitoring infrastructure
