package de.vctrade.finchat.ingest.application.example.fetching;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingService;
import de.vctrade.finchat.ingest.domain.service.UnsupportedSourceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.net.URI;
import java.util.Map;

/**
 * Example usage of the ContentFetchingService demonstrating various scenarios.
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ContentFetchingExample {

    private final ContentFetchingService contentFetchingService;

    /**
     * Example: Fetch a PDF document from an HTTP URL.
     */
    public Mono<String> fetchHttpDocument() {
        FetchRequest request = FetchRequest.builder()
                .source(URI.create("https://example.com/document.pdf"))
                .headers(Map.of("Authorization", "Bearer token123"))
                .maxSizeBytes(50 * 1024 * 1024L) // 50MB limit
                .timeoutMs(30000) // 30 seconds
                .build();

        return contentFetchingService.fetchContent(request)
                .doOnNext(result -> {
                    log.info("Fetched document: {} bytes, content-type: {}",
                            result.getContentLength(), result.getContentType());

                    // Process the content stream here
                    try (var stream = result.getContentStream()) {
                        // Use the stream for further processing
                        log.info("Processing content stream...");
                    } catch (IOException e) {
                        log.error("Error processing content stream", e);
                    }
                })
                .map(result -> "Successfully fetched: " + result.getSource())
                .doOnError(error -> log.error("Failed to fetch document", error));
    }

    /**
     * Example: Fetch a local file from the file system.
     */
    public Mono<String> fetchLocalFile() {
        FetchRequest request = FetchRequest.builder()
                .source(URI.create("file:///tmp/local-document.pdf"))
                .maxSizeBytes(100 * 1024 * 1024L) // 100MB limit
                .build();

        return contentFetchingService.fetchContent(request)
                .doOnNext(result -> {
                    log.info("Fetched local file: {} bytes, last modified: {}",
                            result.getContentLength(), result.getLastModified());
                })
                .map(result -> "Successfully fetched local file: " + result.getSource())
                .doOnError(error -> log.error("Failed to fetch local file", error));
    }

    /**
     * Example: Check if a source is supported before fetching.
     */
    public Mono<String> safeFetch(String sourceUrl) {
        URI source = URI.create(sourceUrl);

        if (!contentFetchingService.canFetch(source)) {
            return Mono.error(new UnsupportedSourceException(source.getScheme()));
        }

        FetchRequest request = FetchRequest.builder()
                .source(source)
                .build();

        return contentFetchingService.fetchContent(request)
                .map(result -> "Fetched: " + result.getSource());
    }

    /**
     * Example: Get all supported schemes.
     */
    public String[] getSupportedSchemes() {
        return contentFetchingService.getSupportedSchemes();
    }
}
