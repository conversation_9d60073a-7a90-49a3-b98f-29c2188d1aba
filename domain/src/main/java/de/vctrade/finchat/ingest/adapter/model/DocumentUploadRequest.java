package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Request model for uploading a document.
 */
@Schema(name = "DocumentUploadRequest", description = "Request model for uploading a document.")
public class DocumentUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("externalId")
    @Schema(description = "External identifier for the document")
    private @Nullable String externalId;

    @JsonProperty("collectionId")
    @Schema(description = "ID of the collection this document belongs to")
    private @Nullable UUID collectionId;

    @JsonProperty("url")
    @Size(max = 2048, message = "URL must not exceed 2048 characters")
    @Schema(description = "URL of the document if uploading from web")
    private @Nullable String url;

    @JsonProperty("autoProcess")
    @Schema(description = "Whether to automatically start processing after upload", defaultValue = "true")
    private @Nullable Boolean autoProcess;

    public DocumentUploadRequest() {
        super();
    }

    public @Nullable String getExternalId() {
        return externalId;
    }

    public void setExternalId(@Nullable String externalId) {
        this.externalId = externalId;
    }

    public @Nullable UUID getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(@Nullable UUID collectionId) {
        this.collectionId = collectionId;
    }

    public @Nullable String getUrl() {
        return url;
    }

    public void setUrl(@Nullable String url) {
        this.url = url;
    }

    public @Nullable Boolean getAutoProcess() {
        return autoProcess;
    }

    public void setAutoProcess(@Nullable Boolean autoProcess) {
        this.autoProcess = autoProcess;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentUploadRequest that = (DocumentUploadRequest) o;
        return Objects.equals(externalId, that.externalId) &&
                Objects.equals(collectionId, that.collectionId) &&
                Objects.equals(url, that.url) &&
                Objects.equals(autoProcess, that.autoProcess);
    }

    @Override
    public int hashCode() {
        return Objects.hash(externalId, collectionId, url, autoProcess);
    }

    @Override
    public String toString() {
        return "DocumentUploadRequest{" +
                "externalId='" + externalId + '\'' +
                ", collectionId=" + collectionId +
                ", url='" + url + '\'' +
                ", autoProcess=" + autoProcess +
                '}';
    }
}
