package de.vctrade.finchat.ingest.domain.service;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Mono;

import java.net.URI;

/**
 * Service for fetching document content from various sources (URLs, file system, etc.).
 * Automatically selects the appropriate fetcher based on the source URI scheme.
 */
public interface ContentFetchingService {

    /**
     * Fetches content from the specified source using the appropriate fetcher.
     *
     * @param request the fetch request containing source and parameters
     * @return a Mono containing the fetch result with content stream and metadata
     */
    @NotNull
    Mono<FetchResult> fetchContent(@NotNull FetchRequest request);

    /**
     * Checks if content can be fetched from the given source.
     *
     * @param source the source URI to check
     * @return true if a fetcher is available for this source, false otherwise
     */
    boolean canFetch(@NotNull URI source);

    /**
     * Returns the supported URI schemes across all registered fetchers.
     *
     * @return array of all supported schemes
     */
    @NotNull
    String[] getSupportedSchemes();
}
