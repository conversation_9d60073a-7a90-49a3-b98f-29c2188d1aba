package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Request model for creating a new document.
 */
@Schema(name = "CreateDocumentRequest", description = "Request model for creating a new document.")
public class CreateDocumentRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("externalId")
    @Schema(description = "External identifier for the document")
    private @Nullable String externalId;

    @JsonProperty("filename")
    @NotBlank(message = "Filename is required")
    @Size(max = 255, message = "Filename must not exceed 255 characters")
    @Schema(description = "Original filename of the document", requiredMode = Schema.RequiredMode.REQUIRED)
    private String filename;

    @JsonProperty("contentType")
    @NotBlank(message = "Content type is required")
    @Schema(description = "MIME type of the document", requiredMode = Schema.RequiredMode.REQUIRED)
    private String contentType;

    @JsonProperty("sizeBytes")
    @NotNull(message = "Size is required")
    @Schema(description = "Size of the document in bytes", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long sizeBytes;

    @JsonProperty("collectionId")
    @Schema(description = "ID of the collection this document belongs to")
    private @Nullable UUID collectionId;

    @JsonProperty("rootId")
    @Schema(description = "ID of the root document if this is a chunk")
    private @Nullable UUID rootId;

    @JsonProperty("url")
    @Schema(description = "URL of the document if fetched from web")
    private @Nullable String url;

    public CreateDocumentRequest() {
        super();
    }

    /**
     * Constructor with required parameters
     */
    public CreateDocumentRequest(String filename, String contentType, Long sizeBytes) {
        this.filename = filename;
        this.contentType = contentType;
        this.sizeBytes = sizeBytes;
    }

    public @Nullable String getExternalId() {
        return externalId;
    }

    public void setExternalId(@Nullable String externalId) {
        this.externalId = externalId;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getSizeBytes() {
        return sizeBytes;
    }

    public void setSizeBytes(Long sizeBytes) {
        this.sizeBytes = sizeBytes;
    }

    public @Nullable UUID getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(@Nullable UUID collectionId) {
        this.collectionId = collectionId;
    }

    public @Nullable UUID getRootId() {
        return rootId;
    }

    public void setRootId(@Nullable UUID rootId) {
        this.rootId = rootId;
    }

    public @Nullable String getUrl() {
        return url;
    }

    public void setUrl(@Nullable String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateDocumentRequest that = (CreateDocumentRequest) o;
        return Objects.equals(externalId, that.externalId) &&
                Objects.equals(filename, that.filename) &&
                Objects.equals(contentType, that.contentType) &&
                Objects.equals(sizeBytes, that.sizeBytes) &&
                Objects.equals(collectionId, that.collectionId) &&
                Objects.equals(rootId, that.rootId) &&
                Objects.equals(url, that.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(externalId, filename, contentType, sizeBytes, collectionId, rootId, url);
    }

    @Override
    public String toString() {
        return "CreateDocumentRequest{" +
                "externalId='" + externalId + '\'' +
                ", filename='" + filename + '\'' +
                ", contentType='" + contentType + '\'' +
                ", sizeBytes=" + sizeBytes +
                ", collectionId=" + collectionId +
                ", rootId=" + rootId +
                ", url='" + url + '\'' +
                '}';
    }
}
