package de.vctrade.finchat.ingest.adapter.api;

import de.vctrade.finchat.ingest.adapter.model.DocumentResponse;
import de.vctrade.finchat.ingest.adapter.model.DocumentUploadRequest;
import de.vctrade.finchat.ingest.adapter.model.DocumentsResponse;
import de.vctrade.finchat.ingest.adapter.model.IngestionJobResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.validation.Valid;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.UUID;

public interface DocumentIngestionController {
    @Operation(
            operationId = "uploadDocument",
            summary = "Upload a document",
            description = "Upload a document file or provide a URL for document ingestion",
            responses = {
                    @ApiResponse(responseCode = "201", description = "Document uploaded successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentResponse.class))),
                    @ApiResponse(responseCode = "400", description = "Invalid request"),
                    @ApiResponse(responseCode = "500", description = "Internal server error")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping(consumes = {MediaType.MULTIPART_FORM_DATA_VALUE, MediaType.APPLICATION_JSON_VALUE})
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    Mono<ResponseEntity<DocumentResponse>> uploadDocument(
            @Parameter(description = "Document file", content = @Content(mediaType = MediaType.MULTIPART_FORM_DATA_VALUE))
            @RequestPart(value = "file", required = false) MultipartFile file,
            @Parameter(description = "Document upload metadata")
            @RequestPart(value = "metadata", required = false) @Valid DocumentUploadRequest metadata
    );

    @Operation(
            operationId = "getDocument",
            summary = "Get document details",
            description = "Retrieve details for a specific document by ID",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Document found",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/{id}")
    ResponseEntity<DocumentResponse> getDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    @Operation(
            operationId = "listDocuments",
            summary = "List documents",
            description = "Retrieve a list of documents with optional filtering",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Documents retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = DocumentsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping
    ResponseEntity<DocumentsResponse> listDocuments(
            @Parameter(name = "collection_id", description = "Filter by collection ID", in = ParameterIn.QUERY)
            @RequestParam(value = "collection_id", required = false) UUID collectionId,
            @Parameter(name = "status", description = "Filter by indexing status", in = ParameterIn.QUERY)
            @RequestParam(value = "status", required = false) String status
    );

    @Operation(
            operationId = "deleteDocument",
            summary = "Delete a document",
            description = "Delete a document by ID",
            responses = {
                    @ApiResponse(responseCode = "204", description = "Document deleted successfully"),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    ResponseEntity<Void> deleteDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    @Operation(
            operationId = "processDocument",
            summary = "Start document processing",
            description = "Start or restart the ingestion process for a document",
            responses = {
                    @ApiResponse(responseCode = "202", description = "Processing started",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Document not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping("/{id}/process")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    Mono<ResponseEntity<IngestionJobResponse>> processDocument(
            @Parameter(name = "id", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    default String extractFilenameFromUrl(String url) {
        try {
            return url.substring(url.lastIndexOf('/') + 1);
        } catch (Exception e) {
            return "document";
        }
    }
}
