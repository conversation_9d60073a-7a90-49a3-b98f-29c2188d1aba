# Content Fetching Service

The Content Fetching Service provides a unified interface for retrieving document content from various sources including HTTP URLs and local file system.

## Overview

The service follows a pluggable architecture where different `ContentFetcher` implementations handle specific URI schemes. The main `ContentFetchingService` automatically selects the appropriate fetcher based on the source URI.

## Components

### Core Interfaces

- **`ContentFetchingService`** - Main service interface for fetching content
- **`ContentFetcher`** - Base interface for source-specific fetchers

### Value Objects

- **`FetchRequest`** - Request containing source URI and fetch parameters
- **`FetchResult`** - Result containing content stream and metadata

### Implementations

- **`HttpContentFetcher`** - Fetches content from HTTP/HTTPS URLs
- **`FileSystemContentFetcher`** - Fetches content from local file system
- **`ContentFetchingServiceImpl`** - Main service orchestrating different fetchers

### Exceptions

- **`ContentFetchingException`** - Base exception for fetch failures
- **`UnsupportedSourceException`** - Thrown for unsupported URI schemes
- **`ContentTooLargeException`** - Thrown when content exceeds size limits

## Supported Sources

### HTTP/HTTPS URLs
- Supports GET requests with custom headers
- Configurable timeout and redirect behavior
- Content type detection from HTTP headers
- Size validation and limits
- Proper error handling for HTTP status codes

### File System
- Local file access with security validation
- Content type detection by file extension
- File metadata extraction (size, modification time)
- Permission and existence checks

## Features

### Content Type Detection
- HTTP: Uses `Content-Type` header
- File System: Uses `Files.probeContentType()` with fallback to extension mapping
- Supports all common document formats (PDF, DOCX, TXT, etc.)

### Size Validation
- Configurable maximum content size (default: 100MB)
- Early validation to prevent memory issues
- Throws `ContentTooLargeException` when limits exceeded

### Resource Management
- Returns `InputStream` for memory-efficient processing
- Proper resource cleanup responsibility on consumer
- Buffered streams for performance

### Error Handling
- Comprehensive exception hierarchy
- Detailed error messages with context
- Proper logging for debugging

### Reactive Programming
- Uses Project Reactor (`Mono`/`Flux`)
- Non-blocking I/O operations
- Proper scheduler usage for blocking operations

## Usage Examples

### Basic HTTP Fetch
```java
@Autowired
private ContentFetchingService contentFetchingService;

public Mono<Void> fetchDocument() {
    FetchRequest request = FetchRequest.builder()
        .source(URI.create("https://example.com/document.pdf"))
        .build();
    
    return contentFetchingService.fetchContent(request)
        .doOnNext(result -> {
            // Process the content stream
            try (InputStream stream = result.getContentStream()) {
                // Use stream for further processing
            }
        })
        .then();
}
```

### HTTP Fetch with Custom Headers
```java
FetchRequest request = FetchRequest.builder()
    .source(URI.create("https://api.example.com/document.pdf"))
    .headers(Map.of("Authorization", "Bearer token123"))
    .maxSizeBytes(50 * 1024 * 1024L) // 50MB limit
    .timeoutMs(30000) // 30 seconds
    .build();
```

### File System Fetch
```java
FetchRequest request = FetchRequest.builder()
    .source(URI.create("file:///path/to/document.pdf"))
    .maxSizeBytes(100 * 1024 * 1024L)
    .build();
```

### Safe Fetch with Validation
```java
URI source = URI.create(sourceUrl);
if (contentFetchingService.canFetch(source)) {
    // Proceed with fetch
} else {
    // Handle unsupported source
}
```

## Configuration

### Request Parameters
- `source` (required) - URI of the content to fetch
- `headers` (optional) - HTTP headers for HTTP requests
- `maxSizeBytes` (optional) - Maximum content size (default: 100MB)
- `timeoutMs` (optional) - HTTP timeout (default: 30 seconds)
- `followRedirects` (optional) - Follow HTTP redirects (default: true)

### Result Metadata
- `contentStream` - Input stream for content
- `source` - Original source URI
- `contentType` - MIME type of content
- `contentLength` - Content size in bytes
- `lastModified` - Last modification timestamp
- `metadata` - Additional headers/properties
- `statusCode` - HTTP status code (for HTTP sources)
- `fetchTimeMs` - Time taken to fetch content

## Integration

The service is designed to integrate seamlessly with the existing document processing pipeline:

1. **Content Fetching** → `ContentFetchingService`
2. **Text Extraction** → `TextExtractionService`
3. **Document Processing** → Other services

## Spring Configuration

The service uses constructor injection and is automatically configured with Spring:

```java
@Service
@RequiredArgsConstructor
public class ContentFetchingServiceImpl implements ContentFetchingService {
    private final List<ContentFetcher> contentFetchers; // Auto-injected
}
```

All `ContentFetcher` implementations are automatically discovered and injected via Spring's component scanning.
