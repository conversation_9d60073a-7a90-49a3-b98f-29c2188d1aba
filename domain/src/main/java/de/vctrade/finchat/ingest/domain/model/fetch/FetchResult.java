package de.vctrade.finchat.ingest.domain.model.fetch;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.InputStream;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Result of content fetching containing the content stream and metadata.
 */
@Data
@Builder
public class FetchResult {

    /**
     * The content stream. Should be closed by the consumer.
     */
    @NotNull
    private final InputStream contentStream;

    /**
     * The source URI that was fetched.
     */
    @NotNull
    private final URI source;

    /**
     * Content type (MIME type) of the fetched content.
     */
    private final String contentType;

    /**
     * Content length in bytes (if known).
     */
    private final Long contentLength;

    /**
     * Last modified timestamp (if available).
     */
    private final LocalDateTime lastModified;

    /**
     * Additional metadata headers from the source.
     */
    private final Map<String, String> metadata;

    /**
     * HTTP status code for HTTP fetches.
     */
    private final Integer statusCode;

    /**
     * Whether the content was retrieved from cache.
     */
    @Builder.Default
    private final boolean fromCache = false;

    /**
     * Time taken to fetch the content in milliseconds.
     */
    private final Long fetchTimeMs;
}
