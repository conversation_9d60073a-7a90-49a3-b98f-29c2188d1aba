package de.vctrade.finchat.ingest.domain.model.fetch;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.net.URI;
import java.util.Map;

/**
 * Request object for fetching content from various sources.
 */
@Data
@Builder
public class FetchRequest {

    /**
     * The source URI to fetch content from (URL for HTTP, file path for filesystem).
     */
    @NotNull
    private final URI source;

    /**
     * Optional headers for HTTP requests.
     */
    private final Map<String, String> headers;

    /**
     * Maximum allowed content size in bytes (default: 100MB).
     */
    @Builder.Default
    private final long maxSizeBytes = 100 * 1024 * 1024L;

    /**
     * Connection timeout in milliseconds for HTTP requests.
     */
    @Builder.Default
    private final long timeoutMillis = 30000;

    /**
     * Whether to follow redirects for HTTP requests.
     */
    @Builder.Default
    private final boolean followRedirects = true;
}
