package de.vctrade.finchat.ingest.application.config;

import de.vctrade.finchat.ingest.adapter.service.TikaTextExtractionService;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingService;
import de.vctrade.finchat.ingest.adapter.service.ContentFetchingServiceImpl;
import de.vctrade.finchat.ingest.adapter.service.FileSystemContentFetcher;
import de.vctrade.finchat.ingest.adapter.service.HttpContentFetcher;
import de.vctrade.finchat.ingest.domain.service.JobOrchestrator;
import de.vctrade.finchat.ingest.adapter.service.ThreadPoolJobOrchestrator;
import de.vctrade.finchat.ingest.domain.service.TextExtractionService;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

/**
 * Main configuration class for the document ingestion subsystem.
 * Configures all beans and services needed for document processing.
 */
@Configuration
@EnableConfigurationProperties({DocumentIngestionProperties.class})
public class DocumentIngestionConfiguration {

    /**
     * Configure the content fetching service with all available fetchers
     */
    @Bean
    @Primary
    public ContentFetchingService contentFetchingService(
            FileSystemContentFetcher fileSystemFetcher,
            HttpContentFetcher httpFetcher) {
        return new ContentFetchingServiceImpl(List.of(fileSystemFetcher, httpFetcher));
    }

    /**
     * Configure the text extraction service using Apache Tika
     */
    @Bean
    @Primary
    public TextExtractionService textExtractionService(DocumentIngestionProperties properties) {
        return new TikaTextExtractionService(
            properties.getTika().getMaxContentLength(),
            properties.getTika().getParseTimeout()
        );
    }

    /**
     * Configure the job orchestrator with thread pool settings
     */
    @Bean
    @Primary
    public JobOrchestrator jobOrchestrator(DocumentIngestionProperties properties) {
        return new ThreadPoolJobOrchestrator(
            properties.getThreadPool().getCoreSize(),
            properties.getThreadPool().getMaxSize(),
            properties.getThreadPool().getQueueCapacity(),
            properties.getJob().getMaxRetryCount(),
            properties.getJob().getRetryDelayMillis()
        );
    }

    /**
     * Scheduled executor for job monitoring and cleanup tasks
     */
    @Bean("documentIngestionScheduler")
    public ScheduledExecutorService scheduledExecutorService() {
        return Executors.newScheduledThreadPool(2);
    }
}
