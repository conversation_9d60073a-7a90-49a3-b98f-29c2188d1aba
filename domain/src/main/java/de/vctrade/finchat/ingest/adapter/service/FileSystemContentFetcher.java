package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import de.vctrade.finchat.ingest.domain.service.ContentFetcher;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingException;
import de.vctrade.finchat.ingest.domain.service.ContentTooLargeException;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Content fetcher for local file system resources.
 */
@Slf4j
@Component
public class FileSystemContentFetcher implements ContentFetcher {

    private static final Set<String> SUPPORTED_SCHEMES = Set.of("file");

    @Override
    public @NotNull Mono<FetchResult> fetch(@NotNull FetchRequest request) {
        return Mono.fromCallable(() -> {
            long startTime = System.currentTimeMillis();
            log.debug("Fetching content from file system source: {}", request.getSource());

            try {
                return performFileFetch(request, startTime);
            } catch (Exception e) {
                log.error("File system fetch failed for source: {}", request.getSource(), e);
                throw new ContentFetchingException("Failed to fetch content from file system: " + request.getSource(), e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> log.info("Successfully fetched {} bytes from {}",
                result.getContentLength(), request.getSource()))
        .doOnError(error -> log.error("File system fetch failed for {}: {}", request.getSource(), error.getMessage()));
    }

    @Override
    public boolean supports(@NotNull URI source) {
        return SUPPORTED_SCHEMES.contains(source.getScheme().toLowerCase());
    }

    @Override
    public @NotNull String[] getSupportedSchemes() {
        return SUPPORTED_SCHEMES.toArray(new String[0]);
    }

    private FetchResult performFileFetch(FetchRequest request, long startTime) throws IOException {
        Path filePath = Paths.get(request.getSource());

        validateFile(filePath, request);

        long fileSize = Files.size(filePath);
        String contentType = detectContentType(filePath);
        LocalDateTime lastModified = extractLastModified(filePath);
        Map<String, String> metadata = extractFileMetadata(filePath);

        BufferedInputStream inputStream = new BufferedInputStream(new FileInputStream(filePath.toFile()));

        long fetchTime = System.currentTimeMillis() - startTime;

        return FetchResult.builder()
                .contentStream(inputStream)
                .source(request.getSource())
                .contentType(contentType)
                .contentLength(fileSize)
                .lastModified(lastModified)
                .metadata(metadata)
                .fetchTimeMs(fetchTime)
                .build();
    }

    private void validateFile(Path filePath, FetchRequest request) throws IOException {
        if (!Files.exists(filePath)) {
            throw new ContentFetchingException("File not found: " + filePath);
        }

        if (!Files.isRegularFile(filePath)) {
            throw new ContentFetchingException("Path is not a regular file: " + filePath);
        }

        if (!Files.isReadable(filePath)) {
            throw new ContentFetchingException("File is not readable: " + filePath);
        }

        long fileSize = Files.size(filePath);
        if (fileSize > request.getMaxSizeBytes()) {
            throw new ContentTooLargeException(fileSize, request.getMaxSizeBytes());
        }
    }

    private String detectContentType(Path filePath) {
        try {
            String contentType = Files.probeContentType(filePath);
            if (contentType != null) {
                return contentType;
            }
        } catch (IOException e) {
            log.debug("Could not probe content type for file: {}", filePath, e);
        }

        // Fallback to extension-based detection
        String filename = filePath.getFileName().toString().toLowerCase();
        if (filename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (filename.endsWith(".docx")) {
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
        } else if (filename.endsWith(".doc")) {
            return "application/msword";
        } else if (filename.endsWith(".txt")) {
            return "text/plain";
        } else if (filename.endsWith(".html") || filename.endsWith(".htm")) {
            return "text/html";
        } else if (filename.endsWith(".xml")) {
            return "application/xml";
        } else if (filename.endsWith(".csv")) {
            return "text/csv";
        } else if (filename.endsWith(".rtf")) {
            return "application/rtf";
        } else if (filename.endsWith(".pptx")) {
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
        } else if (filename.endsWith(".ppt")) {
            return "application/vnd.ms-powerpoint";
        } else if (filename.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (filename.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        }

        return "application/octet-stream";
    }

    private LocalDateTime extractLastModified(Path filePath) {
        try {
            return LocalDateTime.ofInstant(
                    Files.getLastModifiedTime(filePath).toInstant(),
                    ZoneId.systemDefault());
        } catch (IOException e) {
            log.debug("Could not get last modified time for file: {}", filePath, e);
            return null;
        }
    }

    private Map<String, String> extractFileMetadata(Path filePath) {
        Map<String, String> metadata = new HashMap<>();

        try {
            metadata.put("file.name", filePath.getFileName().toString());
            metadata.put("file.path", filePath.toAbsolutePath().toString());
            metadata.put("file.size", String.valueOf(Files.size(filePath)));

            // Add file attributes if available
            if (Files.isHidden(filePath)) {
                metadata.put("file.hidden", "true");
            }

            if (Files.isSymbolicLink(filePath)) {
                metadata.put("file.symbolic_link", "true");
            }

        } catch (IOException e) {
            log.debug("Could not extract all metadata for file: {}", filePath, e);
        }

        return metadata;
    }
}
