package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import de.vctrade.finchat.ingest.domain.service.ContentFetcher;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingService;
import de.vctrade.finchat.ingest.domain.service.UnsupportedSourceException;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Main implementation of content fetching service that coordinates different content fetchers.
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ContentFetchingServiceImpl implements ContentFetchingService {

    private final List<ContentFetcher> contentFetchers;

    @Override
    public @NotNull Mono<FetchResult> fetchContent(@NotNull FetchRequest request) {
        log.debug("Fetching content from source: {}", request.getSource());

        return Mono.fromSupplier(() -> findFetcher(request.getSource()).orElse(null))
                .switchIfEmpty(Mono.error(new UnsupportedSourceException(request.getSource().getScheme())))
                .flatMap(fetcher -> fetcher.fetch(request))
                .doOnSuccess(result -> log.info("Successfully fetched content from {} using {} scheme",
                        request.getSource(), request.getSource().getScheme()))
                .doOnError(error -> log.error("Content fetching failed for {}: {}",
                        request.getSource(), error.getMessage()));
    }

    @Override
    public boolean canFetch(@NotNull URI source) {
        return findFetcher(source).isPresent();
    }

    @Override
    public @NotNull String[] getSupportedSchemes() {
        return contentFetchers.stream()
                .flatMap(fetcher -> Arrays.stream(fetcher.getSupportedSchemes()))
                .distinct()
                .toArray(String[]::new);
    }

    private Optional<ContentFetcher> findFetcher(URI source) {
        return contentFetchers.stream()
                .filter(fetcher -> fetcher.supports(source))
                .findFirst();
    }
}
