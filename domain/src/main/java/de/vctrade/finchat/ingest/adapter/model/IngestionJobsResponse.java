package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Response model containing a list of ingestion jobs.
 */
@Schema(name = "IngestionJobsResponse", description = "Response model containing a list of ingestion jobs.")
public class IngestionJobsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("jobs")
    @Valid
    @Schema(description = "List of ingestion jobs")
    private List<IngestionJobResponse> jobs = new ArrayList<>();

    @JsonProperty("totalCount")
    @Schema(description = "Total number of jobs available")
    private Long totalCount;

    public IngestionJobsResponse() {
        super();
    }

    public IngestionJobsResponse(List<IngestionJobResponse> jobs) {
        this.jobs = jobs != null ? jobs : new ArrayList<>();
        this.totalCount = (long) this.jobs.size();
    }

    public IngestionJobsResponse(List<IngestionJobResponse> jobs, Long totalCount) {
        this.jobs = jobs != null ? jobs : new ArrayList<>();
        this.totalCount = totalCount;
    }

    public List<IngestionJobResponse> getJobs() {
        return jobs;
    }

    public void setJobs(List<IngestionJobResponse> jobs) {
        this.jobs = jobs;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        IngestionJobsResponse that = (IngestionJobsResponse) o;
        return Objects.equals(jobs, that.jobs) &&
                Objects.equals(totalCount, that.totalCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(jobs, totalCount);
    }

    @Override
    public String toString() {
        return "IngestionJobsResponse{" +
                "jobs=" + jobs +
                ", totalCount=" + totalCount +
                '}';
    }
}
