package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Response model containing a list of documents.
 */
@Schema(name = "DocumentsResponse", description = "Response model containing a list of documents.")
public class DocumentsResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("documents")
    @Valid
    @Schema(description = "List of documents")
    private List<DocumentResponse> documents = new ArrayList<>();

    @JsonProperty("totalCount")
    @Schema(description = "Total number of documents available")
    private Long totalCount;

    public DocumentsResponse() {
        super();
    }

    public DocumentsResponse(List<DocumentResponse> documents) {
        this.documents = documents != null ? documents : new ArrayList<>();
        this.totalCount = (long) this.documents.size();
    }

    public DocumentsResponse(List<DocumentResponse> documents, Long totalCount) {
        this.documents = documents != null ? documents : new ArrayList<>();
        this.totalCount = totalCount;
    }

    public List<DocumentResponse> getDocuments() {
        return documents;
    }

    public void setDocuments(List<DocumentResponse> documents) {
        this.documents = documents;
    }

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentsResponse that = (DocumentsResponse) o;
        return Objects.equals(documents, that.documents) &&
                Objects.equals(totalCount, that.totalCount);
    }

    @Override
    public int hashCode() {
        return Objects.hash(documents, totalCount);
    }

    @Override
    public String toString() {
        return "DocumentsResponse{" +
                "documents=" + documents +
                ", totalCount=" + totalCount +
                '}';
    }
}
