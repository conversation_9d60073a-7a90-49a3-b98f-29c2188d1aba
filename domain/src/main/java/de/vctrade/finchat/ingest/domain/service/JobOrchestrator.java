package de.vctrade.finchat.ingest.domain.service;

import reactor.core.publisher.Mono;

import java.util.UUID;

/**
 * High-level interface for job orchestration and management.
 * Handles job scheduling, execution lifecycle, and resource allocation.
 */
public interface JobOrchestrator {

    /**
     * Submit a job for async execution
     * @param jobId unique job identifier
     * @param executor the executor to run the job
     * @return Mono that completes when job is submitted successfully
     */
    Mono<Void> submitJob(UUID jobId, JobExecutor executor);

    /**
     * Cancel a running or queued job
     * @param jobId the job to cancel
     * @return Mono that completes when cancellation is processed
     */
    Mono<Boolean> cancelJob(UUID jobId);

    /**
     * Get current active job count
     * @return number of currently executing jobs
     */
    int getActiveJobCount();

    /**
     * Shutdown the orchestrator gracefully
     * @return Mono that completes when shutdown is finished
     */
    Mono<Void> shutdown();
}
