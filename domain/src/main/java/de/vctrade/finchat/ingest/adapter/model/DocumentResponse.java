package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Response model for document details.
 */
@Schema(name = "DocumentResponse", description = "Response model for document details.")
public class DocumentResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("id")
    private UUID id;

    @JsonProperty("externalId")
    private @Nullable String externalId;

    @JsonProperty("filename")
    private String filename;

    @JsonProperty("contentType")
    private String contentType;

    @JsonProperty("sizeBytes")
    private Long sizeBytes;

    @JsonProperty("collectionId")
    private @Nullable UUID collectionId;

    @JsonProperty("rootId")
    private @Nullable UUID rootId;

    @JsonProperty("indexingStatus")
    private String indexingStatus;

    @JsonProperty("errorMessage")
    private @Nullable String errorMessage;

    @JsonProperty("createdAt")
    private Long createdAt;

    @JsonProperty("updatedAt")
    private @Nullable Long updatedAt;

    public DocumentResponse() {
        super();
    }

    /**
     * Constructor with required parameters
     */
    public DocumentResponse(UUID id, String filename, String contentType, Long sizeBytes, String indexingStatus, Long createdAt) {
        this.id = id;
        this.filename = filename;
        this.contentType = contentType;
        this.sizeBytes = sizeBytes;
        this.indexingStatus = indexingStatus;
        this.createdAt = createdAt;
    }

    public UUID getId() {
        return id;
    }

    public void setId(UUID id) {
        this.id = id;
    }

    public @Nullable String getExternalId() {
        return externalId;
    }

    public void setExternalId(@Nullable String externalId) {
        this.externalId = externalId;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public Long getSizeBytes() {
        return sizeBytes;
    }

    public void setSizeBytes(Long sizeBytes) {
        this.sizeBytes = sizeBytes;
    }

    public @Nullable UUID getCollectionId() {
        return collectionId;
    }

    public void setCollectionId(@Nullable UUID collectionId) {
        this.collectionId = collectionId;
    }

    public @Nullable UUID getRootId() {
        return rootId;
    }

    public void setRootId(@Nullable UUID rootId) {
        this.rootId = rootId;
    }

    public String getIndexingStatus() {
        return indexingStatus;
    }

    public void setIndexingStatus(String indexingStatus) {
        this.indexingStatus = indexingStatus;
    }

    public @Nullable String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(@Nullable String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Long createdAt) {
        this.createdAt = createdAt;
    }

    public @Nullable Long getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(@Nullable Long updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DocumentResponse that = (DocumentResponse) o;
        return Objects.equals(id, that.id) &&
                Objects.equals(externalId, that.externalId) &&
                Objects.equals(filename, that.filename) &&
                Objects.equals(contentType, that.contentType) &&
                Objects.equals(sizeBytes, that.sizeBytes) &&
                Objects.equals(collectionId, that.collectionId) &&
                Objects.equals(rootId, that.rootId) &&
                Objects.equals(indexingStatus, that.indexingStatus) &&
                Objects.equals(errorMessage, that.errorMessage) &&
                Objects.equals(createdAt, that.createdAt) &&
                Objects.equals(updatedAt, that.updatedAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, externalId, filename, contentType, sizeBytes, collectionId, rootId, indexingStatus, errorMessage, createdAt, updatedAt);
    }

    @Override
    public String toString() {
        return "DocumentResponse{" +
                "id=" + id +
                ", externalId='" + externalId + '\'' +
                ", filename='" + filename + '\'' +
                ", contentType='" + contentType + '\'' +
                ", sizeBytes=" + sizeBytes +
                ", collectionId=" + collectionId +
                ", rootId=" + rootId +
                ", indexingStatus='" + indexingStatus + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
