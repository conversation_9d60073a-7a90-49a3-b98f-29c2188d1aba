package de.vctrade.finchat.ingest.domain.service;

/**
 * Exception thrown when content size exceeds the allowed limit.
 */
public class ContentTooLargeException extends ContentFetchingException {

    public ContentTooLargeException(long actualSize, long maxSize) {
        super(String.format("Content size (%d bytes) exceeds maximum allowed size (%d bytes)",
                actualSize, maxSize));
    }
}
