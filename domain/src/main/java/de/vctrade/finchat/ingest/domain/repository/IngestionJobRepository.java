package de.vctrade.finchat.ingest.domain.repository;

import de.vctrade.finchat.ingest.domain.model.ingest.CreateIngestionJobData;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import de.vctrade.finchat.ingest.domain.model.ingest.IngestionJob;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

public interface IngestionJobRepository {

    Mono<IngestionJob> create(final @NotNull CreateIngestionJobData ingestionJob);

    Flux<IngestionJob> fetchAll();

    Mono<IngestionJob> fetchById(@NotNull final UUID id);

    Flux<IngestionJob> fetchByStatus(@NotNull final IndexingStage status);

    Flux<IngestionJob> fetchByDocumentId(@NotNull final UUID documentId);

    Mono<Void> updateStage(
            @NotNull final UUID id,
            @NotNull final IndexingStage indexingStage,
            @Nullable final String errorMessage
    );

}
