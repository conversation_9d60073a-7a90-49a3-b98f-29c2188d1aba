package de.vctrade.finchat.ingest.application.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * Configuration properties for document ingestion system
 */
@ConfigurationProperties(prefix = "finchat.document.ingestion")
@Validated
public class DocumentIngestionProperties {

    @Valid
    @NotNull
    private ThreadPool threadPool = new ThreadPool();

    @Valid
    @NotNull
    private Job job = new Job();

    @Valid
    @NotNull
    private Tika tika = new Tika();

    @Valid
    @NotNull
    private FileSize fileSize = new FileSize();

    @Valid
    @NotNull
    private Http http = new Http();

    public ThreadPool getThreadPool() {
        return threadPool;
    }

    public void setThreadPool(ThreadPool threadPool) {
        this.threadPool = threadPool;
    }

    public Job getJob() {
        return job;
    }

    public void setJob(Job job) {
        this.job = job;
    }

    public Tika getTika() {
        return tika;
    }

    public void setTika(Tika tika) {
        this.tika = tika;
    }

    public FileSize getFileSize() {
        return fileSize;
    }

    public void setFileSize(FileSize fileSize) {
        this.fileSize = fileSize;
    }

    public Http getHttp() {
        return http;
    }

    public void setHttp(Http http) {
        this.http = http;
    }

    /**
     * Thread pool configuration
     */
    public static class ThreadPool {
        @Min(1)
        @Max(50)
        private int coreSize = 4;

        @Min(1)
        @Max(100)
        private int maxSize = 8;

        @Min(10)
        @Max(10000)
        private int queueCapacity = 100;

        public int getCoreSize() {
            return coreSize;
        }

        public void setCoreSize(int coreSize) {
            this.coreSize = coreSize;
        }

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        public int getQueueCapacity() {
            return queueCapacity;
        }

        public void setQueueCapacity(int queueCapacity) {
            this.queueCapacity = queueCapacity;
        }
    }

    /**
     * Job execution configuration
     */
    public static class Job {
        @Min(0)
        @Max(10)
        private int maxRetryCount = 3;

        @Min(1000)
        @Max(300000)
        private long retryDelayMillis = 5000;

        @Min(10000)
        @Max(3600000)
        private long jobTimeoutMillis = 300000; // 5 minutes

        public int getMaxRetryCount() {
            return maxRetryCount;
        }

        public void setMaxRetryCount(int maxRetryCount) {
            this.maxRetryCount = maxRetryCount;
        }

        public long getRetryDelayMillis() {
            return retryDelayMillis;
        }

        public void setRetryDelayMillis(long retryDelayMillis) {
            this.retryDelayMillis = retryDelayMillis;
        }

        public long getJobTimeoutMillis() {
            return jobTimeoutMillis;
        }

        public void setJobTimeoutMillis(long jobTimeoutMillis) {
            this.jobTimeoutMillis = jobTimeoutMillis;
        }
    }

    /**
     * Apache Tika configuration
     */
    public static class Tika {
        @Min(1024)
        @Max(104857600) // 100MB
        private long maxContentLength = 52428800; // 50MB

        @Min(5000)
        @Max(300000)
        private long parseTimeout = 60000; // 1 minute

        private boolean enableOcr = false;

        public long getMaxContentLength() {
            return maxContentLength;
        }

        public void setMaxContentLength(long maxContentLength) {
            this.maxContentLength = maxContentLength;
        }

        public long getParseTimeout() {
            return parseTimeout;
        }

        public void setParseTimeout(long parseTimeout) {
            this.parseTimeout = parseTimeout;
        }

        public boolean isEnableOcr() {
            return enableOcr;
        }

        public void setEnableOcr(boolean enableOcr) {
            this.enableOcr = enableOcr;
        }
    }

    /**
     * File size limits
     */
    public static class FileSize {
        @Min(1024)
        @Max(1073741824) // 1GB
        private long maxUploadSize = 104857600; // 100MB

        @Min(1024)
        @Max(1073741824) // 1GB
        private long maxDownloadSize = 52428800; // 50MB

        public long getMaxUploadSize() {
            return maxUploadSize;
        }

        public void setMaxUploadSize(long maxUploadSize) {
            this.maxUploadSize = maxUploadSize;
        }

        public long getMaxDownloadSize() {
            return maxDownloadSize;
        }

        public void setMaxDownloadSize(long maxDownloadSize) {
            this.maxDownloadSize = maxDownloadSize;
        }
    }

    /**
     * HTTP client configuration
     */
    public static class Http {
        @Min(1000)
        @Max(300000)
        private long connectTimeoutMillis = 10000;

        @Min(1000)
        @Max(600000)
        private long readTimeoutMillis = 60000;

        @Min(1)
        @Max(10)
        private int maxRedirects = 3;

        public long getConnectTimeoutMillis() {
            return connectTimeoutMillis;
        }

        public void setConnectTimeoutMillis(long connectTimeoutMillis) {
            this.connectTimeoutMillis = connectTimeoutMillis;
        }

        public long getReadTimeoutMillis() {
            return readTimeoutMillis;
        }

        public void setReadTimeoutMillis(long readTimeoutMillis) {
            this.readTimeoutMillis = readTimeoutMillis;
        }

        public int getMaxRedirects() {
            return maxRedirects;
        }

        public void setMaxRedirects(int maxRedirects) {
            this.maxRedirects = maxRedirects;
        }
    }
}
