package de.vctrade.finchat.ingest.domain.model.document;

import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.FieldDefaults;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Getter
@Builder
@ToString
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class DocumentAggregate {

    private UUID id;

    private DocumentMetadata documentMetadata;

    private ContentMetadata contentMetadata;

    private ContentNodeData contentNodeData;

    private IndexingStage indexingStage;

}
