package de.vctrade.finchat.ingest.domain.model.extraction;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.time.LocalDateTime;

/**
 * Metadata extracted from a document during text extraction.
 */
@Builder
public record ExtractionMetadata(
        // The original filename of the document.
        @NotNull
        String filename,

        // The MIME type of the document.
        @NotNull
        String contentType,

        //The document title (if available).
        String title,

        // The document author (if available).
        String author,

        //The document subject (if available).
        String subject,

        // The creation date of the document (if available).
        LocalDateTime creationDate,

        // The last modification date of the document (if available).
        LocalDateTime modificationDate,

        // The number of pages in the document (0 if not applicable).
        int pageCount,

        // The language of the document (if detected).
        String language,

        // The character encoding of the document (if applicable).
        String encoding
) {}
