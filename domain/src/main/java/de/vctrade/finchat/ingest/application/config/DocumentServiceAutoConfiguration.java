package de.vctrade.finchat.ingest.application.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;

/**
 * Auto-configuration for Document Service components.
 * Ensures proper initialization and configuration of the document ingestion subsystem.
 */
@AutoConfiguration
@ConditionalOnClass(name = "de.vctrade.finchat.ingest.application.handler.DocumentIngestionIntegrationHandler")
@EnableConfigurationProperties({DocumentIngestionProperties.class})
@Import({DocumentIngestionConfiguration.class})
public class DocumentServiceAutoConfiguration {

    // Auto-configuration is handled through @Import and @EnableConfigurationProperties
    // All beans are configured in DocumentIngestionConfiguration
}
