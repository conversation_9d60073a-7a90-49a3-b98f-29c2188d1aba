package de.vctrade.finchat.ingest.domain.service;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Mono;

import java.net.URI;

/**
 * Base interface for fetching content from different sources.
 */
public interface ContentFetcher {

    /**
     * Fetches content from the specified source.
     *
     * @param request the fetch request containing source and parameters
     * @return a Mono containing the fetch result with content stream and metadata
     */
    @NotNull
    Mono<FetchResult> fetch(@NotNull FetchRequest request);

    /**
     * Checks if this fetcher supports the given URI scheme.
     *
     * @param source the source URI to check
     * @return true if this fetcher can handle the source, false otherwise
     */
    boolean supports(@NotNull URI source);

    /**
     * Returns the supported URI schemes for this fetcher.
     *
     * @return array of supported schemes (e.g., "http", "https", "file")
     */
    @NotNull
    String[] getSupportedSchemes();
}
