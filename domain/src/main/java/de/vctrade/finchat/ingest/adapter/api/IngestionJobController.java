package de.vctrade.finchat.ingest.adapter.api;

import de.vctrade.finchat.ingest.adapter.model.IngestionJobResponse;
import de.vctrade.finchat.ingest.adapter.model.IngestionJobsResponse;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

public interface IngestionJobController {
    @Operation(
            operationId = "getIngestionJob",
            summary = "Get ingestion job status",
            description = "Retrieve status and details for a specific ingestion job by ID",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Job found",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Job not found")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/{id}")
    Mono<ResponseEntity<IngestionJobResponse>> getIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    @Operation(
            operationId = "listIngestionJobs",
            summary = "List ingestion jobs",
            description = "Retrieve a list of ingestion jobs with optional filtering",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping
    Mono<IngestionJobsResponse> listIngestionJobs(
            @Parameter(name = "status", description = "Filter by job status", in = ParameterIn.QUERY)
            @RequestParam(value = "stage", required = false) IndexingStage stage,
            @Parameter(name = "document_id", description = "Filter by document ID", in = ParameterIn.QUERY)
            @RequestParam(value = "document_id", required = false) UUID documentId
    );

    @Operation(
            operationId = "retryIngestionJob",
            summary = "Retry a failed ingestion job",
            description = "Retry a failed ingestion job if retry limit not exceeded",
            responses = {
                    @ApiResponse(responseCode = "202", description = "Job retry started",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobResponse.class))),
                    @ApiResponse(responseCode = "404", description = "Job not found"),
                    @ApiResponse(responseCode = "409", description = "Job cannot be retried")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @PostMapping("/{id}/retry")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    Mono<ResponseEntity<UUID>> retryIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    @Operation(
            operationId = "cancelIngestionJob",
            summary = "Cancel a running ingestion job",
            description = "Cancel a running or pending ingestion job",
            responses = {
                    @ApiResponse(responseCode = "204", description = "Job cancelled successfully"),
                    @ApiResponse(responseCode = "404", description = "Job not found"),
                    @ApiResponse(responseCode = "409", description = "Job cannot be cancelled")
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @DeleteMapping("/{id}")
    @PreAuthorize("isAuthenticated() and !isAnonymous()")
    Mono<ResponseEntity<Void>> cancelIngestionJob(
            @Parameter(name = "id", description = "Ingestion job ID", required = true, in = ParameterIn.PATH)
            @PathVariable("id") UUID id
    );

    @Operation(
            operationId = "getJobsByDocument",
            summary = "Get jobs for a specific document",
            description = "Retrieve all ingestion jobs for a specific document",
            responses = {
                    @ApiResponse(responseCode = "200", description = "Jobs retrieved successfully",
                            content = @Content(mediaType = "application/json", schema = @Schema(implementation = IngestionJobsResponse.class)))
            },
            security = {@SecurityRequirement(name = "OAuth2PasswordBearer")}
    )
    @GetMapping("/document/{documentId}")
    Flux<IngestionJobsResponse> getJobsByDocument(
            @Parameter(name = "documentId", description = "Document ID", required = true, in = ParameterIn.PATH)
            @PathVariable("documentId") UUID documentId
    );
}
