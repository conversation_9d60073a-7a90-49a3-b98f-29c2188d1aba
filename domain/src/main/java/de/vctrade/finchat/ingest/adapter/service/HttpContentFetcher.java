package de.vctrade.finchat.ingest.adapter.service;

import de.vctrade.finchat.ingest.domain.model.fetch.FetchRequest;
import de.vctrade.finchat.ingest.domain.model.fetch.FetchResult;
import de.vctrade.finchat.ingest.domain.service.ContentFetcher;
import de.vctrade.finchat.ingest.domain.service.ContentFetchingException;
import de.vctrade.finchat.ingest.domain.service.ContentTooLargeException;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Content fetcher for HTTP and HTTPS URLs.
 */
@Slf4j
@Component
public class HttpContentFetcher implements ContentFetcher {

    private static final Set<String> SUPPORTED_SCHEMES = Set.of("http", "https");
    private static final String USER_AGENT = "FinChat-DocumentService/1.0";

    private final HttpClient httpClient;

    public HttpContentFetcher() {
        this.httpClient = HttpClient.newBuilder()
                .followRedirects(HttpClient.Redirect.NORMAL)
                .build();
    }

    @Override
    public @NotNull Mono<FetchResult> fetch(@NotNull FetchRequest request) {
        return Mono.fromCallable(() -> {
            long startTime = System.currentTimeMillis();
            log.debug("Fetching content from HTTP source: {}", request.getSource());

            try {
                return performHttpFetch(request, startTime);
            } catch (Exception e) {
                log.error("HTTP fetch failed for source: {}", request.getSource(), e);
                throw new ContentFetchingException("Failed to fetch content from HTTP source: " + request.getSource(), e);
            }
        })
        .subscribeOn(Schedulers.boundedElastic())
        .doOnSuccess(result -> log.info("Successfully fetched {} bytes from {}",
                result.getContentLength(), request.getSource()))
        .doOnError(error -> log.error("HTTP fetch failed for {}: {}", request.getSource(), error.getMessage()));
    }

    @Override
    public boolean supports(@NotNull URI source) {
        return SUPPORTED_SCHEMES.contains(source.getScheme().toLowerCase());
    }

    @Override
    public @NotNull String[] getSupportedSchemes() {
        return SUPPORTED_SCHEMES.toArray(new String[0]);
    }

    private FetchResult performHttpFetch(FetchRequest request, long startTime) throws IOException, InterruptedException {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(request.getSource())
                .timeout(Duration.ofMillis(request.getTimeoutMillis()))
                .header("User-Agent", USER_AGENT);

        // Add custom headers
        if (request.getHeaders() != null) {
            request.getHeaders().forEach(requestBuilder::header);
        }

        HttpRequest httpRequest = requestBuilder.build();

        HttpClient clientToUse = request.isFollowRedirects() ? httpClient :
                HttpClient.newBuilder().followRedirects(HttpClient.Redirect.NEVER).build();

        HttpResponse<byte[]> response = clientToUse.send(httpRequest, HttpResponse.BodyHandlers.ofByteArray());

        validateResponse(response, request);

        long fetchTime = System.currentTimeMillis() - startTime;

        return FetchResult.builder()
                .contentStream(new ByteArrayInputStream(response.body()))
                .source(request.getSource())
                .contentType(extractContentType(response))
                .contentLength((long) response.body().length)
                .lastModified(extractLastModified(response))
                .metadata(extractMetadata(response))
                .statusCode(response.statusCode())
                .fetchTimeMs(fetchTime)
                .build();
    }

    private void validateResponse(HttpResponse<byte[]> response, FetchRequest request) {
        // Check HTTP status
        if (response.statusCode() >= 400) {
            throw new ContentFetchingException(
                    String.format("HTTP request failed with status %d for source: %s",
                            response.statusCode(), request.getSource()));
        }

        // Check content size
        long contentLength = response.body().length;
        if (contentLength > request.getMaxSizeBytes()) {
            throw new ContentTooLargeException(contentLength, request.getMaxSizeBytes());
        }
    }

    private String extractContentType(HttpResponse<byte[]> response) {
        return response.headers().firstValue("content-type")
                .map(ct -> ct.split(";")[0].trim()) // Remove charset part
                .orElse("application/octet-stream");
    }

    private LocalDateTime extractLastModified(HttpResponse<byte[]> response) {
        return response.headers().firstValue("last-modified")
                .map(this::parseHttpDate)
                .orElse(null);
    }

    private LocalDateTime parseHttpDate(String httpDate) {
        try {
            // HTTP dates are typically in RFC 1123 format
            return LocalDateTime.parse(httpDate, DateTimeFormatter.RFC_1123_DATE_TIME);
        } catch (Exception e) {
            log.debug("Could not parse HTTP date: {}", httpDate);
            return null;
        }
    }

    private Map<String, String> extractMetadata(HttpResponse<byte[]> response) {
        Map<String, String> metadata = new HashMap<>();

        // Add relevant headers as metadata
        response.headers().map().forEach((name, values) -> {
            if (!values.isEmpty() && isRelevantHeader(name)) {
                metadata.put(name, values.get(0));
            }
        });

        return metadata;
    }

    private boolean isRelevantHeader(String headerName) {
        String lower = headerName.toLowerCase();
        return lower.equals("etag") ||
               lower.equals("cache-control") ||
               lower.equals("expires") ||
               lower.equals("server") ||
               lower.equals("content-encoding") ||
               lower.equals("content-language") ||
               lower.equals("content-disposition");
    }
}
