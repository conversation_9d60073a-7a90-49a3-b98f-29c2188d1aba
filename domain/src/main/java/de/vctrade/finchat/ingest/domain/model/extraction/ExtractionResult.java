package de.vctrade.finchat.ingest.domain.model.extraction;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Result of text extraction containing the extracted text, metadata, and structure information.
 */
@Data
@Builder
public class ExtractionResult {

    /**
     * The raw extracted text content.
     */
    @NotNull
    private final String text;

    /**
     * Text content chunked into logical sections for processing.
     */
    @NotNull
    private final List<TextChunk> chunks;

    /**
     * Document metadata extracted during processing.
     */
    @NotNull
    private final ExtractionMetadata metadata;

    /**
     * Additional properties extracted from the document.
     */
    @NotNull
    private final Map<String, String> properties;
}
