package de.vctrade.finchat.ingest.domain.repository;

import de.vctrade.finchat.ingest.domain.model.document.CreateDocumentData;
import de.vctrade.finchat.ingest.domain.model.document.DocumentAggregate;
import de.vctrade.finchat.ingest.domain.model.ingest.IndexingStage;
import jakarta.annotation.Nullable;
import jakarta.validation.constraints.NotNull;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.UUID;

public interface DocumentRepository {

    Mono<DocumentAggregate> create(@NotNull CreateDocumentData data);

    Flux<DocumentAggregate> fetchAll();

    Mono<DocumentAggregate> fetchById(@NotNull UUID id);

    Mono<DocumentAggregate> fetchByExternalId(@NotNull String externalId);

    Flux<DocumentAggregate> fetchByRootId(@NotNull UUID rootId);

    Flux<DocumentAggregate> fetchByIndexingStatus(@NotNull final IndexingStage status);

    Mono<Void> updateIndexingStage(
            @NotNull final UUID id,
            @NotNull final IndexingStage indexingStage,
            @Nullable final String errorMessage
    );

}
