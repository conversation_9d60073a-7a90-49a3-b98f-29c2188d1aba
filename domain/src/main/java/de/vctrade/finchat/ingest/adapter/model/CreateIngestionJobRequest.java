package de.vctrade.finchat.ingest.adapter.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import org.springframework.lang.Nullable;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

/**
 * Request model for creating a new ingestion job.
 */
@Schema(name = "CreateIngestionJobRequest", description = "Request model for creating a new ingestion job.")
public class CreateIngestionJobRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonProperty("documentId")
    @NotNull(message = "Document ID is required")
    @Schema(description = "ID of the document to be ingested", requiredMode = Schema.RequiredMode.REQUIRED)
    private UUID documentId;

    @JsonProperty("priority")
    @Schema(description = "Job priority (higher number = higher priority)", defaultValue = "0")
    private @Nullable Integer priority;

    @JsonProperty("maxRetries")
    @Schema(description = "Maximum number of retry attempts", defaultValue = "3")
    private @Nullable Integer maxRetries;

    public CreateIngestionJobRequest() {
        super();
    }

    /**
     * Constructor with required parameters
     */
    public CreateIngestionJobRequest(UUID documentId) {
        this.documentId = documentId;
    }

    public UUID getDocumentId() {
        return documentId;
    }

    public void setDocumentId(UUID documentId) {
        this.documentId = documentId;
    }

    public @Nullable Integer getPriority() {
        return priority;
    }

    public void setPriority(@Nullable Integer priority) {
        this.priority = priority;
    }

    public @Nullable Integer getMaxRetries() {
        return maxRetries;
    }

    public void setMaxRetries(@Nullable Integer maxRetries) {
        this.maxRetries = maxRetries;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CreateIngestionJobRequest that = (CreateIngestionJobRequest) o;
        return Objects.equals(documentId, that.documentId) &&
                Objects.equals(priority, that.priority) &&
                Objects.equals(maxRetries, that.maxRetries);
    }

    @Override
    public int hashCode() {
        return Objects.hash(documentId, priority, maxRetries);
    }

    @Override
    public String toString() {
        return "CreateIngestionJobRequest{" +
                "documentId=" + documentId +
                ", priority=" + priority +
                ", maxRetries=" + maxRetries +
                '}';
    }
}
