package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import de.vctrade.finchat.domain.generated.dto.ResponseChunkType;
import java.util.UUID;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ContentResponseChunk
 */


public class ContentResponseChunk implements Serializable {

  private static final long serialVersionUID = 1L;

  private @Nullable ResponseChunkType type;

  private @Nullable UUID messageId;

  private @Nullable String content;

  public ContentResponseChunk type(ResponseChunkType type) {
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
   */
  @Valid 
  @Schema(name = "type", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("type")
  public ResponseChunkType getType() {
    return type;
  }

  public void setType(ResponseChunkType type) {
    this.type = type;
  }

  public ContentResponseChunk messageId(UUID messageId) {
    this.messageId = messageId;
    return this;
  }

  /**
   * Get messageId
   * @return messageId
   */
  @Valid 
  @Schema(name = "messageId", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("messageId")
  public UUID getMessageId() {
    return messageId;
  }

  public void setMessageId(UUID messageId) {
    this.messageId = messageId;
  }

  public ContentResponseChunk content(String content) {
    this.content = content;
    return this;
  }

  /**
   * Get content
   * @return content
   */
  
  @Schema(name = "content", example = "answer from AI chat", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("content")
  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ContentResponseChunk contentResponseChunk = (ContentResponseChunk) o;
    return Objects.equals(this.type, contentResponseChunk.type) &&
        Objects.equals(this.messageId, contentResponseChunk.messageId) &&
        Objects.equals(this.content, contentResponseChunk.content);
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, messageId, content);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ContentResponseChunk {\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    messageId: ").append(toIndentedString(messageId)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ContentResponseChunk instance;

    public Builder() {
      this(new ContentResponseChunk());
    }

    protected Builder(ContentResponseChunk instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ContentResponseChunk value) { 
      this.instance.setType(value.type);
      this.instance.setMessageId(value.messageId);
      this.instance.setContent(value.content);
      return this;
    }

    public ContentResponseChunk.Builder type(ResponseChunkType type) {
      this.instance.type(type);
      return this;
    }
    
    public ContentResponseChunk.Builder messageId(UUID messageId) {
      this.instance.messageId(messageId);
      return this;
    }
    
    public ContentResponseChunk.Builder content(String content) {
      this.instance.content(content);
      return this;
    }
    
    /**
    * returns a built ContentResponseChunk instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ContentResponseChunk build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ContentResponseChunk.Builder builder() {
    return new ContentResponseChunk.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ContentResponseChunk.Builder toBuilder() {
    ContentResponseChunk.Builder builder = new ContentResponseChunk.Builder();
    return builder.copyOf(this);
  }

}

