package de.vctrade.finchat.domain.validation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

@Documented
@Constraint(validatedBy = MessageRequestRoleValidator.class)
@Target({ElementType.FIELD, ElementType.PARAMETER}) // Can also be used on parameters if needed
@Retention(RetentionPolicy.RUNTIME)
public @interface ValidMessageRequestRole {
    String message() default "Role must be 'user' for MessageRequest.";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}