package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import de.vctrade.finchat.domain.validation.ValidMessageRequestRole;
import de.vctrade.finchat.domain.generated.dto.ChatSettings;
import de.vctrade.finchat.domain.generated.dto.Role;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Request model for a chat message.
 */

@Schema(name = "MessageRequest", description = "Request model for a chat message.")

public class MessageRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  @ValidMessageRequestRole
  private Role role = Role.USER;

  private String content;

  private @Nullable ChatSettings chatSettings;

  public MessageRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public MessageRequest(String content) {
    this.content = content;
  }

  public MessageRequest role(Role role) {
    this.role = role;
    return this;
  }

  /**
   * Get role
   * @return role
   */
  @Valid 
  @Schema(name = "role", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("role")
  public Role getRole() {
    return role;
  }

  public void setRole(Role role) {
    this.role = role;
  }

  public MessageRequest content(String content) {
    this.content = content;
    return this;
  }

  /**
   * Get content
   * @return content
   */
  @NotNull @Size(max = 50000) 
  @Schema(name = "content", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("content")
  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public MessageRequest chatSettings(ChatSettings chatSettings) {
    this.chatSettings = chatSettings;
    return this;
  }

  /**
   * Get chatSettings
   * @return chatSettings
   */
  @Valid 
  @Schema(name = "chatSettings", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("chatSettings")
  public ChatSettings getChatSettings() {
    return chatSettings;
  }

  public void setChatSettings(ChatSettings chatSettings) {
    this.chatSettings = chatSettings;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MessageRequest messageRequest = (MessageRequest) o;
    return Objects.equals(this.role, messageRequest.role) &&
        Objects.equals(this.content, messageRequest.content) &&
        Objects.equals(this.chatSettings, messageRequest.chatSettings);
  }

  @Override
  public int hashCode() {
    return Objects.hash(role, content, chatSettings);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MessageRequest {\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    chatSettings: ").append(toIndentedString(chatSettings)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private MessageRequest instance;

    public Builder() {
      this(new MessageRequest());
    }

    protected Builder(MessageRequest instance) {
      this.instance = instance;
    }

    protected Builder copyOf(MessageRequest value) { 
      this.instance.setRole(value.role);
      this.instance.setContent(value.content);
      this.instance.setChatSettings(value.chatSettings);
      return this;
    }

    public MessageRequest.Builder role(Role role) {
      this.instance.role(role);
      return this;
    }
    
    public MessageRequest.Builder content(String content) {
      this.instance.content(content);
      return this;
    }
    
    public MessageRequest.Builder chatSettings(ChatSettings chatSettings) {
      this.instance.chatSettings(chatSettings);
      return this;
    }
    
    /**
    * returns a built MessageRequest instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public MessageRequest build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static MessageRequest.Builder builder() {
    return new MessageRequest.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public MessageRequest.Builder toBuilder() {
    MessageRequest.Builder builder = new MessageRequest.Builder();
    return builder.copyOf(this);
  }

}

