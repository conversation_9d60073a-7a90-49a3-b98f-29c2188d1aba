package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Request model for creating a conversation.
 */

@Schema(name = "CreateConversationRequest", description = "Request model for creating a conversation.")

public class CreateConversationRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private UUID correlationId;

  @Valid
  private @Nullable List<UUID> sourceIds;

  private @Nullable String summary = null;

  public CreateConversationRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public CreateConversationRequest(UUID correlationId) {
    this.correlationId = correlationId;
  }

  public CreateConversationRequest correlationId(UUID correlationId) {
    this.correlationId = correlationId;
    return this;
  }

  /**
   * Get correlationId
   * @return correlationId
   */
  @NotNull @Valid 
  @Schema(name = "correlationId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("correlationId")
  public UUID getCorrelationId() {
    return correlationId;
  }

  public void setCorrelationId(UUID correlationId) {
    this.correlationId = correlationId;
  }

  public CreateConversationRequest sourceIds(List<UUID> sourceIds) {
    this.sourceIds = sourceIds;
    return this;
  }

  public CreateConversationRequest addSourceIdsItem(UUID sourceIdsItem) {
    if (this.sourceIds == null) {
      this.sourceIds = new ArrayList<>();
    }
    this.sourceIds.add(sourceIdsItem);
    return this;
  }

  /**
   * Get sourceIds
   * @return sourceIds
   */
  @Valid 
  @Schema(name = "sourceIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceIds")
  public List<UUID> getSourceIds() {
    return sourceIds;
  }

  public void setSourceIds(List<UUID> sourceIds) {
    this.sourceIds = sourceIds;
  }

  public CreateConversationRequest summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * Get summary
   * @return summary
   */
  @Size(max = 120) 
  @Schema(name = "summary", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("summary")
  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CreateConversationRequest createConversationRequest = (CreateConversationRequest) o;
    return Objects.equals(this.correlationId, createConversationRequest.correlationId) &&
        Objects.equals(this.sourceIds, createConversationRequest.sourceIds) &&
        Objects.equals(this.summary, createConversationRequest.summary);
  }

  @Override
  public int hashCode() {
    return Objects.hash(correlationId, sourceIds, summary);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CreateConversationRequest {\n");
    sb.append("    correlationId: ").append(toIndentedString(correlationId)).append("\n");
    sb.append("    sourceIds: ").append(toIndentedString(sourceIds)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private CreateConversationRequest instance;

    public Builder() {
      this(new CreateConversationRequest());
    }

    protected Builder(CreateConversationRequest instance) {
      this.instance = instance;
    }

    protected Builder copyOf(CreateConversationRequest value) { 
      this.instance.setCorrelationId(value.correlationId);
      this.instance.setSourceIds(value.sourceIds);
      this.instance.setSummary(value.summary);
      return this;
    }

    public CreateConversationRequest.Builder correlationId(UUID correlationId) {
      this.instance.correlationId(correlationId);
      return this;
    }
    
    public CreateConversationRequest.Builder sourceIds(List<UUID> sourceIds) {
      this.instance.sourceIds(sourceIds);
      return this;
    }
    
    public CreateConversationRequest.Builder summary(String summary) {
      this.instance.summary(summary);
      return this;
    }
    
    /**
    * returns a built CreateConversationRequest instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public CreateConversationRequest build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static CreateConversationRequest.Builder builder() {
    return new CreateConversationRequest.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public CreateConversationRequest.Builder toBuilder() {
    CreateConversationRequest.Builder builder = new CreateConversationRequest.Builder();
    return builder.copyOf(this);
  }

}

