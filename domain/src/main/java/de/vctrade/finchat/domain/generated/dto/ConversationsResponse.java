package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import de.vctrade.finchat.domain.generated.dto.ConversationResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Response model for listing conversations.
 */

@Schema(name = "ConversationsResponse", description = "Response model for listing conversations.")

public class ConversationsResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  @Valid
  private List<@Valid ConversationResponse> conversations = new ArrayList<>();

  public ConversationsResponse() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ConversationsResponse(List<@Valid ConversationResponse> conversations) {
    this.conversations = conversations;
  }

  public ConversationsResponse conversations(List<@Valid ConversationResponse> conversations) {
    this.conversations = conversations;
    return this;
  }

  public ConversationsResponse addConversationsItem(ConversationResponse conversationsItem) {
    if (this.conversations == null) {
      this.conversations = new ArrayList<>();
    }
    this.conversations.add(conversationsItem);
    return this;
  }

  /**
   * Get conversations
   * @return conversations
   */
  @NotNull @Valid 
  @Schema(name = "conversations", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("conversations")
  public List<@Valid ConversationResponse> getConversations() {
    return conversations;
  }

  public void setConversations(List<@Valid ConversationResponse> conversations) {
    this.conversations = conversations;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ConversationsResponse conversationsResponse = (ConversationsResponse) o;
    return Objects.equals(this.conversations, conversationsResponse.conversations);
  }

  @Override
  public int hashCode() {
    return Objects.hash(conversations);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ConversationsResponse {\n");
    sb.append("    conversations: ").append(toIndentedString(conversations)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ConversationsResponse instance;

    public Builder() {
      this(new ConversationsResponse());
    }

    protected Builder(ConversationsResponse instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ConversationsResponse value) { 
      this.instance.setConversations(value.conversations);
      return this;
    }

    public ConversationsResponse.Builder conversations(List<ConversationResponse> conversations) {
      this.instance.conversations(conversations);
      return this;
    }
    
    /**
    * returns a built ConversationsResponse instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ConversationsResponse build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ConversationsResponse.Builder builder() {
    return new ConversationsResponse.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ConversationsResponse.Builder toBuilder() {
    ConversationsResponse.Builder builder = new ConversationsResponse.Builder();
    return builder.copyOf(this);
  }

}

