package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import de.vctrade.finchat.domain.generated.dto.MessageRequest;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Request model for a chat conversation.
 */

@Schema(name = "ChatRequest", description = "Request model for a chat conversation.")

public class ChatRequest implements Serializable {

  private static final long serialVersionUID = 1L;

  private MessageRequest message;

  public ChatRequest() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ChatRequest(MessageRequest message) {
    this.message = message;
  }

  public ChatRequest message(MessageRequest message) {
    this.message = message;
    return this;
  }

  /**
   * Get message
   * @return message
   */
  @NotNull @Valid 
  @Schema(name = "message", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("message")
  public MessageRequest getMessage() {
    return message;
  }

  public void setMessage(MessageRequest message) {
    this.message = message;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChatRequest chatRequest = (ChatRequest) o;
    return Objects.equals(this.message, chatRequest.message);
  }

  @Override
  public int hashCode() {
    return Objects.hash(message);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChatRequest {\n");
    sb.append("    message: ").append(toIndentedString(message)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ChatRequest instance;

    public Builder() {
      this(new ChatRequest());
    }

    protected Builder(ChatRequest instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ChatRequest value) { 
      this.instance.setMessage(value.message);
      return this;
    }

    public ChatRequest.Builder message(MessageRequest message) {
      this.instance.message(message);
      return this;
    }
    
    /**
    * returns a built ChatRequest instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ChatRequest build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ChatRequest.Builder builder() {
    return new ChatRequest.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ChatRequest.Builder toBuilder() {
    ChatRequest.Builder builder = new ChatRequest.Builder();
    return builder.copyOf(this);
  }

}

