package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import de.vctrade.finchat.domain.generated.dto.MessageResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Response model for chat history.
 */

@Schema(name = "ChatHistoryResponse", description = "Response model for chat history.")

public class ChatHistoryResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private UUID conversationId;

  @Valid
  private List<@Valid MessageResponse> messages = new ArrayList<>();

  public ChatHistoryResponse() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ChatHistoryResponse(UUID conversationId, List<@Valid MessageResponse> messages) {
    this.conversationId = conversationId;
    this.messages = messages;
  }

  public ChatHistoryResponse conversationId(UUID conversationId) {
    this.conversationId = conversationId;
    return this;
  }

  /**
   * Get conversationId
   * @return conversationId
   */
  @NotNull @Valid 
  @Schema(name = "conversationId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("conversationId")
  public UUID getConversationId() {
    return conversationId;
  }

  public void setConversationId(UUID conversationId) {
    this.conversationId = conversationId;
  }

  public ChatHistoryResponse messages(List<@Valid MessageResponse> messages) {
    this.messages = messages;
    return this;
  }

  public ChatHistoryResponse addMessagesItem(MessageResponse messagesItem) {
    if (this.messages == null) {
      this.messages = new ArrayList<>();
    }
    this.messages.add(messagesItem);
    return this;
  }

  /**
   * Get messages
   * @return messages
   */
  @NotNull @Valid 
  @Schema(name = "messages", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("messages")
  public List<@Valid MessageResponse> getMessages() {
    return messages;
  }

  public void setMessages(List<@Valid MessageResponse> messages) {
    this.messages = messages;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChatHistoryResponse chatHistoryResponse = (ChatHistoryResponse) o;
    return Objects.equals(this.conversationId, chatHistoryResponse.conversationId) &&
        Objects.equals(this.messages, chatHistoryResponse.messages);
  }

  @Override
  public int hashCode() {
    return Objects.hash(conversationId, messages);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChatHistoryResponse {\n");
    sb.append("    conversationId: ").append(toIndentedString(conversationId)).append("\n");
    sb.append("    messages: ").append(toIndentedString(messages)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ChatHistoryResponse instance;

    public Builder() {
      this(new ChatHistoryResponse());
    }

    protected Builder(ChatHistoryResponse instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ChatHistoryResponse value) { 
      this.instance.setConversationId(value.conversationId);
      this.instance.setMessages(value.messages);
      return this;
    }

    public ChatHistoryResponse.Builder conversationId(UUID conversationId) {
      this.instance.conversationId(conversationId);
      return this;
    }
    
    public ChatHistoryResponse.Builder messages(List<MessageResponse> messages) {
      this.instance.messages(messages);
      return this;
    }
    
    /**
    * returns a built ChatHistoryResponse instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ChatHistoryResponse build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ChatHistoryResponse.Builder builder() {
    return new ChatHistoryResponse.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ChatHistoryResponse.Builder toBuilder() {
    ChatHistoryResponse.Builder builder = new ChatHistoryResponse.Builder();
    return builder.copyOf(this);
  }

}

