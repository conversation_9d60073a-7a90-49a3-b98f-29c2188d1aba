package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import de.vctrade.finchat.domain.generated.dto.Role;
import java.util.UUID;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Response model for a chat message.
 */

@Schema(name = "MessageResponse", description = "Response model for a chat message.")

public class MessageResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private UUID id;

  private Role role = Role.USER;

  private Integer order;

  private String content;

  private Long createdAt;

  public MessageResponse() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public MessageResponse(UUID id, Role role, Integer order, String content, Long createdAt) {
    this.id = id;
    this.role = role;
    this.order = order;
    this.content = content;
    this.createdAt = createdAt;
  }

  public MessageResponse id(UUID id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @NotNull @Valid 
  @Schema(name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("id")
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public MessageResponse role(Role role) {
    this.role = role;
    return this;
  }

  /**
   * Get role
   * @return role
   */
  @NotNull @Valid 
  @Schema(name = "role", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("role")
  public Role getRole() {
    return role;
  }

  public void setRole(Role role) {
    this.role = role;
  }

  public MessageResponse order(Integer order) {
    this.order = order;
    return this;
  }

  /**
   * Get order
   * @return order
   */
  @NotNull 
  @Schema(name = "order", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("order")
  public Integer getOrder() {
    return order;
  }

  public void setOrder(Integer order) {
    this.order = order;
  }

  public MessageResponse content(String content) {
    this.content = content;
    return this;
  }

  /**
   * Get content
   * @return content
   */
  @NotNull 
  @Schema(name = "content", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("content")
  public String getContent() {
    return content;
  }

  public void setContent(String content) {
    this.content = content;
  }

  public MessageResponse createdAt(Long createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Epoch & Unix Timestamp in in milliseconds
   * @return createdAt
   */
  @NotNull 
  @Schema(name = "createdAt", description = "Epoch & Unix Timestamp in in milliseconds", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("createdAt")
  public Long getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(Long createdAt) {
    this.createdAt = createdAt;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MessageResponse messageResponse = (MessageResponse) o;
    return Objects.equals(this.id, messageResponse.id) &&
        Objects.equals(this.role, messageResponse.role) &&
        Objects.equals(this.order, messageResponse.order) &&
        Objects.equals(this.content, messageResponse.content) &&
        Objects.equals(this.createdAt, messageResponse.createdAt);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, role, order, content, createdAt);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MessageResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    role: ").append(toIndentedString(role)).append("\n");
    sb.append("    order: ").append(toIndentedString(order)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private MessageResponse instance;

    public Builder() {
      this(new MessageResponse());
    }

    protected Builder(MessageResponse instance) {
      this.instance = instance;
    }

    protected Builder copyOf(MessageResponse value) { 
      this.instance.setId(value.id);
      this.instance.setRole(value.role);
      this.instance.setOrder(value.order);
      this.instance.setContent(value.content);
      this.instance.setCreatedAt(value.createdAt);
      return this;
    }

    public MessageResponse.Builder id(UUID id) {
      this.instance.id(id);
      return this;
    }
    
    public MessageResponse.Builder role(Role role) {
      this.instance.role(role);
      return this;
    }
    
    public MessageResponse.Builder order(Integer order) {
      this.instance.order(order);
      return this;
    }
    
    public MessageResponse.Builder content(String content) {
      this.instance.content(content);
      return this;
    }
    
    public MessageResponse.Builder createdAt(Long createdAt) {
      this.instance.createdAt(createdAt);
      return this;
    }
    
    /**
    * returns a built MessageResponse instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public MessageResponse build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static MessageResponse.Builder builder() {
    return new MessageResponse.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public MessageResponse.Builder toBuilder() {
    MessageResponse.Builder builder = new MessageResponse.Builder();
    return builder.copyOf(this);
  }

}

