package de.vctrade.finchat.domain.validation;

import de.vctrade.finchat.domain.generated.dto.Role;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class MessageRequestRoleValidator implements ConstraintValidator<ValidMessageRequestRole, Role> {

    @Override
    public void initialize(ValidMessageRequestRole constraintAnnotation) {
        // No initialization needed for this simple case
    }

    @Override
    public boolean isValid(Role value, ConstraintValidatorContext context) {
        // MessageRequest can use only Role.USER
        return value == null || value == Role.USER;
    }
}