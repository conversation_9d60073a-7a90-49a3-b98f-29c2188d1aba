package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * Response model for conversation details.
 */

@Schema(name = "ConversationResponse", description = "Response model for conversation details.")

public class ConversationResponse implements Serializable {

  private static final long serialVersionUID = 1L;

  private UUID id;

  private UUID correlationId;

  private Long createdAt;

  private @Nullable String summary;

  @Valid
  private @Nullable List<UUID> sourceIds;

  public ConversationResponse() {
    super();
  }

  /**
   * Constructor with only required parameters
   */
  public ConversationResponse(UUID id, UUID correlationId, Long createdAt) {
    this.id = id;
    this.correlationId = correlationId;
    this.createdAt = createdAt;
  }

  public ConversationResponse id(UUID id) {
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @NotNull @Valid 
  @Schema(name = "id", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("id")
  public UUID getId() {
    return id;
  }

  public void setId(UUID id) {
    this.id = id;
  }

  public ConversationResponse correlationId(UUID correlationId) {
    this.correlationId = correlationId;
    return this;
  }

  /**
   * Get correlationId
   * @return correlationId
   */
  @NotNull @Valid 
  @Schema(name = "correlationId", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("correlationId")
  public UUID getCorrelationId() {
    return correlationId;
  }

  public void setCorrelationId(UUID correlationId) {
    this.correlationId = correlationId;
  }

  public ConversationResponse createdAt(Long createdAt) {
    this.createdAt = createdAt;
    return this;
  }

  /**
   * Epoch & Unix Timestamp in in milliseconds
   * @return createdAt
   */
  @NotNull 
  @Schema(name = "createdAt", description = "Epoch & Unix Timestamp in in milliseconds", requiredMode = Schema.RequiredMode.REQUIRED)
  @JsonProperty("createdAt")
  public Long getCreatedAt() {
    return createdAt;
  }

  public void setCreatedAt(Long createdAt) {
    this.createdAt = createdAt;
  }

  public ConversationResponse summary(String summary) {
    this.summary = summary;
    return this;
  }

  /**
   * Get summary
   * @return summary
   */
  @Size(max = 120) 
  @Schema(name = "summary", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("summary")
  public String getSummary() {
    return summary;
  }

  public void setSummary(String summary) {
    this.summary = summary;
  }

  public ConversationResponse sourceIds(List<UUID> sourceIds) {
    this.sourceIds = sourceIds;
    return this;
  }

  public ConversationResponse addSourceIdsItem(UUID sourceIdsItem) {
    if (this.sourceIds == null) {
      this.sourceIds = new ArrayList<>();
    }
    this.sourceIds.add(sourceIdsItem);
    return this;
  }

  /**
   * Get sourceIds
   * @return sourceIds
   */
  @Valid 
  @Schema(name = "sourceIds", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("sourceIds")
  public List<UUID> getSourceIds() {
    return sourceIds;
  }

  public void setSourceIds(List<UUID> sourceIds) {
    this.sourceIds = sourceIds;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ConversationResponse conversationResponse = (ConversationResponse) o;
    return Objects.equals(this.id, conversationResponse.id) &&
        Objects.equals(this.correlationId, conversationResponse.correlationId) &&
        Objects.equals(this.createdAt, conversationResponse.createdAt) &&
        Objects.equals(this.summary, conversationResponse.summary) &&
        Objects.equals(this.sourceIds, conversationResponse.sourceIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, correlationId, createdAt, summary, sourceIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ConversationResponse {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    correlationId: ").append(toIndentedString(correlationId)).append("\n");
    sb.append("    createdAt: ").append(toIndentedString(createdAt)).append("\n");
    sb.append("    summary: ").append(toIndentedString(summary)).append("\n");
    sb.append("    sourceIds: ").append(toIndentedString(sourceIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ConversationResponse instance;

    public Builder() {
      this(new ConversationResponse());
    }

    protected Builder(ConversationResponse instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ConversationResponse value) { 
      this.instance.setId(value.id);
      this.instance.setCorrelationId(value.correlationId);
      this.instance.setCreatedAt(value.createdAt);
      this.instance.setSummary(value.summary);
      this.instance.setSourceIds(value.sourceIds);
      return this;
    }

    public ConversationResponse.Builder id(UUID id) {
      this.instance.id(id);
      return this;
    }
    
    public ConversationResponse.Builder correlationId(UUID correlationId) {
      this.instance.correlationId(correlationId);
      return this;
    }
    
    public ConversationResponse.Builder createdAt(Long createdAt) {
      this.instance.createdAt(createdAt);
      return this;
    }
    
    public ConversationResponse.Builder summary(String summary) {
      this.instance.summary(summary);
      return this;
    }
    
    public ConversationResponse.Builder sourceIds(List<UUID> sourceIds) {
      this.instance.sourceIds(sourceIds);
      return this;
    }
    
    /**
    * returns a built ConversationResponse instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ConversationResponse build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ConversationResponse.Builder builder() {
    return new ConversationResponse.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ConversationResponse.Builder toBuilder() {
    ConversationResponse.Builder builder = new ConversationResponse.Builder();
    return builder.copyOf(this);
  }

}

