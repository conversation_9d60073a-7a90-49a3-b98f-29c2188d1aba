package de.vctrade.finchat.domain.generated.dto;

import java.net.URI;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import org.springframework.lang.Nullable;
import java.io.Serializable;
import java.time.OffsetDateTime;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import io.swagger.v3.oas.annotations.media.Schema;


import java.util.*;
import jakarta.annotation.Generated;

/**
 * ChatSettings
 */


public class ChatSettings implements Serializable {

  private static final long serialVersionUID = 1L;

  private @Nullable Boolean useAdvancedModel;

  public ChatSettings useAdvancedModel(Boolean useAdvancedModel) {
    this.useAdvancedModel = useAdvancedModel;
    return this;
  }

  /**
   * used to set technical attributes for chat
   * @return useAdvancedModel
   */
  
  @Schema(name = "useAdvancedModel", example = "false", description = "used to set technical attributes for chat", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("useAdvancedModel")
  public Boolean getUseAdvancedModel() {
    return useAdvancedModel;
  }

  public void setUseAdvancedModel(Boolean useAdvancedModel) {
    this.useAdvancedModel = useAdvancedModel;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ChatSettings chatSettings = (ChatSettings) o;
    return Objects.equals(this.useAdvancedModel, chatSettings.useAdvancedModel);
  }

  @Override
  public int hashCode() {
    return Objects.hash(useAdvancedModel);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ChatSettings {\n");
    sb.append("    useAdvancedModel: ").append(toIndentedString(useAdvancedModel)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  
  public static class Builder {

    private ChatSettings instance;

    public Builder() {
      this(new ChatSettings());
    }

    protected Builder(ChatSettings instance) {
      this.instance = instance;
    }

    protected Builder copyOf(ChatSettings value) { 
      this.instance.setUseAdvancedModel(value.useAdvancedModel);
      return this;
    }

    public ChatSettings.Builder useAdvancedModel(Boolean useAdvancedModel) {
      this.instance.useAdvancedModel(useAdvancedModel);
      return this;
    }
    
    /**
    * returns a built ChatSettings instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public ChatSettings build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static ChatSettings.Builder builder() {
    return new ChatSettings.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public ChatSettings.Builder toBuilder() {
    ChatSettings.Builder builder = new ChatSettings.Builder();
    return builder.copyOf(this);
  }

}

