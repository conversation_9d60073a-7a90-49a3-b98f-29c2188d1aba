openapi: 3.0.1
info:
  title: Finnbot API
  description: API for document chat with AI assistance
  version: 0.1.0
servers:
  - url: /finnbot-api
paths:
  /conversations:
    get:
      tags:
        - Conversations API
      summary: List all conversations. If security is enabled, only the current user's conversations are returned.
      description: "List all conversations.\n\nReturns:\n    List of all conversations"
      operationId: list_conversations_conversations_get
      security:
        - OAuth2PasswordBearer: []
      parameters:
        - name: correlation_id
          in: query
          required: false
          schema:
            anyOf:
              - type: string
                format: uuid
              - type: null
            description: Return only conversations with this correlation ID
            title: Correlation Id
          description: Return only conversations with this correlation ID
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConversationsResponse"
        "422":
          description: Validation Error
    post:
      tags:
        - Conversations API
      summary: Create a new conversation
      description: "Create a new conversation. \nReturns:\n    The ID of the newly created conversation"
      operationId: create_conversation_conversations_create_post
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateConversationRequest"
        required: true
      responses:
        "201":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConversationResponse"
        "422":
          description: Validation Error
      security:
        - OAuth2PasswordBearer: []
  /conversations/{conversation_id}:
    get:
      tags:
        - Conversations API
      summary: Get conversation details
      description: "Get details for a specific conversation.\n\nArgs:\n    conversation_id: The ID of the conversation\n\nReturns:\n    Conversation details"
      operationId: get_conversation_conversations__conversation_id__get
      security:
        - OAuth2PasswordBearer: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            title: Conversation Id
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ConversationResponse"
        "422":
          description: Validation Error
  /conversations/{conversation_id}/chat:
    get:
      tags:
        - Conversations API
      summary: Get chat history for a conversation
      description: "Get the chat history for a conversation, excluding system messages.\n\nArgs:\n    conversation_id: The ID of the conversation\n\nReturns:\n    Chat history with messages between user and assistant"
      operationId: get_chat_history_conversations__conversation_id__chat_get
      security:
        - OAuth2PasswordBearer: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            title: Conversation Id
      responses:
        "200":
          description: Successful Response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChatHistoryResponse"
        "422":
          description: Validation Error
    post:
      tags:
        - Conversations API
      summary: Send a message and get a streaming response
      description: "Send a message to a conversation and get a streaming response.\n\nThis endpoint uses Server-Sent Events (SSE) to stream the assistant's\nresponse back to the client in chunks, allowing for a more interactive\nexperience.\n\nArgs:\n    conversation_id: The ID of the conversation\n    request: The chat request containing messages\n\nReturns:\n    A streaming response with the assistant's message"
      operationId: add_chat_message_conversations__conversation_id__chat_post
      security:
        - OAuth2PasswordBearer: []
      parameters:
        - name: conversation_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
            title: Conversation Id
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ChatRequest"
      responses:
        "200":
          description: Successful Response
          content:
            text/event-stream:
              schema:
                type: string
                format: event-stream # Custom format to indicate SSE
                description: Each event in the stream is a string.
        "422":
          description: Validation Error
components:
  schemas:
    ChatRequest:
      properties:
        message:
          $ref: "#/components/schemas/MessageRequest"
          title: Message
      type: object
      required:
        - message
      title: ChatRequest
      description: Request model for a chat conversation.
    ConversationResponse:
      properties:
        id:
          type: string
          format: uuid
          title: Id
        correlationId:
          type: string
          format: uuid
          title: Correlationid
        createdAt:
          type: integer
          format: int64
          description: Epoch & Unix Timestamp in in milliseconds
          title: Createdat
        summary:
          type: string
          maxLength: 120
          title: Summary
        sourceIds:
          anyOf:
            - items:
                type: string
                format: uuid
              type: array
            - type: null
          title: Sourceids
      type: object
      required:
        - id
        - correlationId
        - createdAt
      title: ConversationResponse
      description: Response model for conversation details.
    ChatHistoryResponse:
      properties:
        conversationId:
          type: string
          format: uuid
          title: Conversationid
        messages:
          items:
            $ref: "#/components/schemas/MessageResponse"
          type: array
          title: Messages
      type: object
      required:
        - conversationId
        - messages
      title: ChatHistoryResponse
      description: Response model for chat history.
    ConversationsResponse:
      properties:
        conversations:
          items:
            $ref: "#/components/schemas/ConversationResponse"
          type: array
          title: Conversations
      type: object
      required:
        - conversations
      title: ConversationsResponse
      description: Response model for listing conversations.
    CreateConversationRequest:
      properties:
        correlationId:
          type: string
          format: uuid
          title: Correlationid
        sourceIds:
          anyOf:
            - items:
                type: string
                format: uuid
              type: array
            - type: null
          title: Sourceids
        summary:
          type: string
          maxLength: 120
          title: Summary
          nullable: true
      type: object
      required:
        - correlationId
      title: CreateConversationRequest
      description: Request model for creating a conversation.
    MessageRequest:
      properties:
        role:
          $ref: '#/components/schemas/Role'
          title: Role
          default: user
        content:
          type: string
          title: Content
          maxLength: 50000
        chatSettings:
          $ref: '#/components/schemas/ChatSettings'
          description: " for example useAdvancedModel = false "
      type: object
      required:
        - content
      title: MessageRequest
      description: Request model for a chat message.
    MessageResponse:
      properties:
        id:
          type: string
          format: uuid
        role:
          $ref: '#/components/schemas/Role'
          title: Role
        order:
          type: integer
          title: Order
        content:
          type: string
          title: Content
        createdAt:
          type: integer
          format: int64
          description: Epoch & Unix Timestamp in in milliseconds
          title: created at
      type: object
      required:
        - id
        - role
        - order
        - content
        - createdAt
      title: MessageResponse
      description: Response model for a chat message.
    Role:
      type: string
      enum:
        - user
        - assistant
        - system
      title: Role
      default: user
    ContentResponseChunk:
      properties:
        type:
          $ref: '#/components/schemas/ResponseChunkType'
          required: true
        messageId:
            type: string
            format: uuid
        content:
          type: string
          example: "answer from AI chat"
    ResponseChunkType:
      type: string
      enum:
        - start
        - end
        - notice
        - content
    ChatSettings:
      properties:
        useAdvancedModel:
          type: boolean
          example: false
          description: used to set technical attributes for chat

  securitySchemes:
    OAuth2PasswordBearer:
      type: oauth2
      flows:
        password:
          scopes: {}
          tokenUrl: auth/token