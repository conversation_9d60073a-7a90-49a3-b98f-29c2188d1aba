package de.vctrade.finchat.service.app;

import de.vctrade.base.service.SecurityServiceImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.web.filter.ForwardedHeaderFilter;

@SpringBootApplication(scanBasePackages = {"de.vctrade.finchat"}, exclude = {SecurityAutoConfiguration.class})
@ComponentScan (
		basePackages = {
				"de.vctrade.base",
				"de.vctrade.finchat",
				"de.vctrade.mongodb.config",

		},
		excludeFilters = {
				@ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, value = SecurityServiceImpl.class) }
)
@EnableMongoRepositories(
		basePackages = {
				"de.vctrade.finchat"
		}
)
@EnableConfigurationProperties
public class FinchatServiceApp extends SpringBootServletInitializer {
	@Override
	protected SpringApplicationBuilder configure(
			SpringApplicationBuilder builder) {
		return builder.sources(FinchatServiceApp.class);
	}

	@Bean
	ForwardedHeaderFilter forwardedHeaderFilter() {
		return new ForwardedHeaderFilter();
	}

	public static void main(String[] args) {
		SpringApplication sa = new SpringApplication(
				FinchatServiceApp.class);
		sa.run(args);
	}
}
