package de.vctrade.finchat.service.config.security;

import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.ReactiveAuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsConfigurationSource;
import org.springframework.web.cors.reactive.UrlBasedCorsConfigurationSource;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebFluxSecurity
@Order(2)
public class WebFluxSecurityConfig {

    @Bean
    public SecurityWebFilterChain springSecurityWebFilterChain(ServerHttpSecurity http,
                                                            @Value("#{'${service.allowed.origins}'.split(',\\s*')}") final @NotNull List<String> allowedOrigins) {
        http
                .csrf(ServerHttpSecurity.CsrfSpec::disable)
                .cors(cors -> cors.configurationSource(reactiveCorsConfigurationSource(allowedOrigins)))

                .authorizeExchange(exchanges -> exchanges

                        .pathMatchers(HttpMethod.POST,"/conversations/{conversation_id}/chat").authenticated()
                        // All other requests in the WebFlux context (if any) require authentication
                        .anyExchange().authenticated()
                );

        return http.build();
    }

    /**
     * corsConfigurationSource beans looks very similar for reactive and for not reactive,
     * but use different implementations for CorsConfigurationSource and for UrlBasedCorsConfigurationSource
     * @param allowedOrigins
     * @return
     */
    @Bean
    public CorsConfigurationSource reactiveCorsConfigurationSource(@NotNull List<String> allowedOrigins) {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(allowedOrigins);
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "OPTIONS", "DELETE"));
        configuration.addAllowedHeader("Authorization");
        configuration.addAllowedHeader("Content-Type");
        configuration.setAllowCredentials(true);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }

    @Bean
    public ReactiveAuthenticationManager reactiveAuthenticationManager() {
        return authentication -> {
            // If the authentication object is already a UsernamePasswordAuthenticationToken
            // and it's marked as authenticated by our filter, just return it.
            // This manager trusts what ReactiveCustomBearerTokenFilter has already done.
            if (authentication instanceof UsernamePasswordAuthenticationToken && authentication.isAuthenticated()) {
                return Mono.just(authentication);
            }
            // Otherwise, this manager doesn't support it or it's not authenticated by us.
            return Mono.error(new BadCredentialsException("Invalid or unsupported authentication for reactive flow."));
        };
    }
}