package de.vctrade.finchat.service.config.database;

import de.vctrade.finchat.model.entity.ConversationEntity;
import de.vctrade.mongodb.util.IndexInitializer;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class MongoDBIndexInitializer implements ApplicationListener<ContextRefreshedEvent> {

    private final MongoTemplate mongoTemplate;

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        IndexInitializer.createIndexIfNotExists(mongoTemplate, ConversationEntity.class, ConversationEntity.Fields.referenceID, Sort.Direction.ASC);
    }
}
