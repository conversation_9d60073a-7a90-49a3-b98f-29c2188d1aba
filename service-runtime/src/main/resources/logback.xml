<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <contextName>context</contextName>
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <springProperty scope="context" name="springAppName" source="spring.application.name"/>
    <springProperty scope="context" name="CLUSTER_NAME" source="vctrade.cluster.name"/>
    <springProperty scope="context" name="BASE_FILE_NAME" source="vctrade.service.log-file-name"/>
    <springProperty scope="context" name="CONSOLE_LOG_LEVEL" source="console.log.level"/>
    <property name="STACKTRACE_EXCLUDE_PATTERN" value="\$\$FastClassByCGLIB\$\$,\$\$EnhancerBySpringCGLIB\$\$,^sun\.reflect\..*\.invoke,^com\.sun\.,^sun\.net\.,^net\.sf\.cglib\.proxy\.MethodProxy\.invoke,^org\.springframework\.cglib\.,^org\.springframework\.transaction\.,^org\.springframework\.validation\.,^org\.springframework\.app\.,^org\.springframework\.aop\.,^java\.lang\.reflect\.Method\.invoke,^org\.springframework\.ws\..*\.invoke,^org\.springframework\.ws\.transport\.,^org\.springframework\.ws\.soap\.saaj\.SaajSoapMessage\.,^org\.springframework\.ws\.client\.core\.WebServiceTemplate\.,^org\.springframework\.web\.filter\.,^org\.apache\.tomcat\.,^org\.apache\.catalina\.,^org\.apache\.coyote\.,^java\.util\.concurrent\.ThreadPoolExecutor\.runWorker,^java\.lang\.Thread\.run$"/>
    <property name="LOG_ROOT_DIR" value="${catalina.base}/logs" />
    <property name="LOG_FILE" value="${LOG_ROOT_DIR}/${BASE_FILE_NAME}"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>${CONSOLE_LOG_LEVEL:-INFO}</level>
        </filter>
    </appender>
    <!-- Appender to log to file in a JSON format -->
    <appender name="FILE_ALL_JSON" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}.json</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.json.%d{yyyy-MM-dd}.gz</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
            <providers>
                <timestamp>
                    <timeZone>UTC</timeZone>
                </timestamp>
                <logLevel>
                    <fieldName>severity</fieldName>
                </logLevel>
                <threadName>
                    <fieldName>thread</fieldName>
                </threadName>
                <loggerName>
                    <fieldName>class</fieldName>
                    <shortenedLoggerNameLength>40</shortenedLoggerNameLength>
                </loggerName>
                <mdc/>
                <arguments/>
                <logstashMarkers/>
                <tags>
                    <fieldName>logTags</fieldName>
                </tags>
                <stackHash>
                    <exclusions>${STACKTRACE_EXCLUDE_PATTERN}</exclusions>
                </stackHash>
                <stackTrace>
                    <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                        <inlineHash>true</inlineHash>
                        <exclusions>${STACKTRACE_EXCLUDE_PATTERN}</exclusions>
                    </throwableConverter>
                </stackTrace>
                <message/>
                <!-- Tracing: Use `trace`, `span` and `parent` if applicable -->
                <pattern>
                    <pattern>
                        {
                        "cluster": "${CLUSTER_NAME:-}",
                        "service": "${springAppName:-}",
                        "pid": "${PID:-}"
                        }
                    </pattern>
                </pattern>
            </providers>
        </encoder>
    </appender>
    <root level="INFO">
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE_ALL_JSON" />
    </root>
</configuration>