logging.level.root=WARN
logging.level.org.springframework=DEBUG
logging.level.de.vctrade=DEBUG
spring.application.name=finchat-service
logging.pattern.level=%d{HH:mm:ss.SSS} [%thread] %-5level [${spring.application.name:},%X{traceId:-},%X{spanId:-}] %logger{36} -%kvp- %msg%n

management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always

mongodb.host=${vctrade.mongodb.host:localhost}
mongodb.port=${vctrade.mongodb.port:27017}
mongodb.user=${vctrade.mongodb.user:vctrade_user-dev}
mongodb.password=${vctrade.mongodb.password:vctrade_user_dev-passw0rd}
mongodb.connectionString.options=${vctrade.mongodb.connectionString.options:readPreference=primary&ssl=false}
spring.data.mongodb.database=${vctrade.mongodb.database:finchat-service}

spring.data.mongodb.connectionString=mongodb://${mongodb.user}:${mongodb.password}@${mongodb.host}:${mongodb.port}/?${mongodb.connectionString.options}
spring.data.mongodb.auto-index-creation=true


server.port=8087
springdoc.api-docs.enabled=true
springdoc.swagger-ui.enabled=true
server.forward-headers-strategy=framework

springdoc.swagger-ui.path=/swagger-ui.html
spring.jackson.default-property-inclusion=non_null

vctrade.api.key-store-location=${vctrade.api-keystore.location:/keystore/devtest30-vctrade-api.jks}
vctrade.api.key-store-password=${vctrade.api-keystore.password:uhs3NXdUgGJXvcNk}
vctrade.api.key-store-alias=${vctrade.api-keystore.alias:vctrade-api}
vctrade.api.key-store-alias-password=${vctrade.api-keystore.alias-password:ObyzneXN7TMirXLj}
vctrade.api.key-id=${vctrade.api-keystore.key-id:b879368f-19d8-49ed-a559-887e84ebfce6}

server.servlet.session.timeout=5m

service.allowed.origins=${vctrade.service.origins:https://prelive.vc-trade.de, https://www.vc-trade.de, https://demo1.vc-trade.de, https://demo2.vc-trade.de, https://demo3.vc-trade.de, https://devtest1.vc-trade.de,https://devtest27.vc-trade.de,https://devtest2.vc-trade.de, https://devtest3.vc-trade.de, https://devtest4.vc-trade.de, https://devtest5.vc-trade.de, https://devtest6.vc-trade.de, https://devtest7.vc-trade.de, https://devtest8.vc-trade.de, https://devtest9.vc-trade.de, https://devtest10.vc-trade.de, https://devtest11.vc-trade.de, https://devtest12.vc-trade.de, https://devtest13.vc-trade.de, https://devtest14.vc-trade.de, https://devtest15.vc-trade.de, https://devtest16.vc-trade.de, https://devtest17.vc-trade.de, https://devtest18.vc-trade.de, https://devtest19.vc-trade.de, https://devtest20.vc-trade.de, https://devtest21.vc-trade.de, https://devtest22.vc-trade.de, https://devtest23.vc-trade.de, https://devtest28.vc-trade.de, http://localhost:8080, http://localhost, https://devtest24.vc-trade.de, https://testcluster.vc-trade.de, , https://devtest32.vc-trade.de}
spring.main.allow-bean-definition-overriding=true