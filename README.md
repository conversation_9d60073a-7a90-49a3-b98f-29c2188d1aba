# Finance Chat and AI tooling service

This project allows to users chat with AI using documents or other
text sources as a context of the chat

## MongoDb

to work with application you'll need installed version
of MongoDb. Easiest way to do it, is to add MongoDb to
your local Docker Desktop

There is a docker-compose configuration available under
*deployment/docker-compose/docker-compose.yaml*

to install mongodb execute following commands:

```cs
cd deployment/docker-compose
docker compose up mongodb -d
```

##MongoDB Indexes

In the case, if you use MongoDB- Transactions, you'll get an issue,
if you try to create first document in transactional context if
you use **@Indexed** annotation

**@Indexed** annotation inform spring boot to create MongoDB index.

```css
    @Indexed
    @Builder.Default
    @NonNull
    @Size(max=255)
    private String email = "";
```

Therefore to prevent such issue, MongoDB Indexes were created during application start.
using **MongoDBIndexInitializer**

```css
    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {

        // Create indexes in this place, outside of any transaction
        // this code is just an example how to create MongoDB indexes 
        IndexInitializer.createIndexIfNotExists(mongoTemplate, CompanyEntity.class, CompanyEntity.Fields.users + "." + UserEntity.Fields.email, Sort.Direction.ASC);
        IndexInitializer.createIndexIfNotExists(mongoTemplate, VerificationCodeEntity.class, VerificationCodeEntity.Fields.login, Sort.Direction.ASC);
        IndexInitializer.createIndexIfNotExists(mongoTemplate, TokenEntity.class, TokenEntity.Fields.tokenValue, Sort.Direction.ASC);
        IndexInitializer.createIndexIfNotExists(mongoTemplate, RegisteredSSOCompanyEntity.class, RegisteredSSOCompanyEntity.Fields.clientId, Sort.Direction.ASC);

    }

```

## MongoDB Transactions

to allow mongodb transactons with spring boot,
you need to inject **MongoTransactionManager**

for example

```cs
@Configuration
public class TransactionManagerConfig {
    @Bean
    MongoTransactionManager transactionManager(MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }
}
```

Now you can use *@Transactional* in your classes.

## Finchat Service with Docker compose

Finchat Service could be deployed using same docker compose configuration
like MongoDb or RabbitMq

please use following commands:

```cs
cd /deployment/docker-compose
docker compose up customer-relation-service -d
```

Docker Compose installation of finchat-service contains

```cs
Tomcat 10.x
JDK 17
finchat-service.war
```

Dockerfile for build is available under

```cs
./Dockerfile
```

After start of docker compose for **customer-relation-service**
application is available under following

URL, for example [Swagger UI][5]

[5]:http://localhost:8087/swagger-ui/index.html

## Customer Relation Service with Docker compose. Configuration

Important hint.

It is not possible to use *localhost* or *127.0.0.1* as a part of connection stings,
for the communication, for example between **Finchat Service** and **MongoDb**
therefore

in **docker-compose.yaml**
connection string for MongoDb is redefined / overwritten

mongodb://vctrade_user-dev:vctrade_user_dev-passw0rd@**mongodb**:27017/?readPreference=primary&ssl=false

as you see *localhost* in connection string was replaced with *mongodb*
It is covered by usage of **driver: bridge** in docker compose.

## Full deployment

In the case if you want to deploy all part of application using **docker compose**,
please do following steps

using IDE of your choise

1. Check, that you use JDK 17 or later
2. Reload all maven projects
3. Maven clean
4. Maven install

then

```cs
cd /deployment/docker-compose
docker compose up -d
```

## Maven

All dependencies, that are required for "Finchat Service" handled by maven.
Just use IDE of your choice to build / start the application
