# Document Ingestion System Architecture

This document describes the architecture in a different project. It serves as
architecture overview ONLY. It is written for a Python backend, and changes
to this outline are expected.

The name of the prototype described here is "Finnbot".

## Overview

The Finnbot document ingestion system implements a comprehensive architecture
for handling document upload, content extraction, text processing, and
hierarchical document management. The system is designed around Domain-Driven
Design (DDD) principles with clear separation of concerns across multiple
layers.

## High-Level Architecture

```mermaid
graph TB
    API[Document API] --> Service[Document Service]
    API --> IndexAPI[Indexing API]
    
    Service --> Repo[Document Repository]
    Service --> Storage[Storage Service]
    Service --> CollRepo[Collection Repository]
    
    IndexAPI --> IndexRepo[Indexing Job Repository]
    IndexAPI --> IndexService[Document Indexing Service]
    
    IndexService --> Repo
    IndexService --> Storage
    IndexService --> IndexRepo
    IndexService --> TextExtractor[Text Extraction Pipeline]
    
    Repo --> DB[(SQLite Database)]
    Storage --> FS[(File System - CAS)]
    
    TextExtractor --> Lang<PERSON>hain[LangChain Document Loaders]
    TextExtractor --> PyMuPDF[PyMuPDF4LLM]
```

## Core Components

### 1. Document Data Model

The system uses a sophisticated hierarchical document model that supports three types of document nodes:

#### Document Types (DocumentNodeType)
- **DOCUMENT**: Root-level uploaded files
- **TEXT_CHUNK**: Extracted text segments from documents
- **FACT**: Standalone text content created directly

#### Document Domain Model
```python
class Document(BaseModel):
    id: Optional[UUID]                      # Internal database ID
    external_document_id: Optional[UUID]   # Public API ID
    filename: Optional[str]
    content_metadata: ContentMetadata       # Hash, size, storage info
    content_type: str                       # MIME type
    uploaded_at: datetime
    collection_id: Optional[UUID]          # Associated collection
    
    # Indexing status tracking
    indexing_job_id: Optional[UUID]
    indexing_stage: Optional[IndexingStage]
    
    # Hierarchical relationships
    root_id: Optional[UUID]                 # Points to root document
    parent_id: Optional[UUID]              # Points to immediate parent
    node_type: DocumentNodeType            # Type of document node
    
    # Content storage
    content: Optional[Union[str, bytes]]   # Inlined content for chunks/facts
    metadata: Dict[str, Any]               # Arbitrary metadata
```

#### Content Metadata
```python
class ContentMetadata(BaseModel):
    hash: str                    # SHA-256 content hash
    size: int                   # Content size in bytes
    inlined_content: bool       # Whether content is stored inline vs external
```

### 2. Storage Architecture

#### Content-Addressed Storage (CAS)
The system implements a content-addressed storage pattern where:
- Files are stored by their SHA-256 hash
- Automatic deduplication prevents duplicate content storage
- Atomic operations ensure consistency during uploads
- Temporary staging area for upload processing

#### Storage Service Interface
```python
class StorageService(Protocol):
    def store_content(self, content_stream: BinaryIO) -> ContentMetadata
    def get_content(self, content_hash: str) -> Generator[Optional[BinaryIO], None, None]
    async def stream_content(self, content_hash: str) -> AsyncGenerator[bytes, None]
```

#### LocalStorageService Implementation
- Uses content hashes as filenames for deduplication
- Implements atomic move operations for consistency
- Provides streaming access for large files
- Manages temporary files during upload processing

### 3. Indexing Job System

The background job system tracks document processing through a state machine:

#### IndexingJob States
- **PENDING**: Job created, waiting to be processed
- **RUNNING**: Job is currently being executed
- **COMPLETED**: Job finished successfully
- **FAILED**: Job encountered an error

#### IndexingStage States
- **PENDING**: Document has not been processed
- **EXTRACTING**: Text extraction is in progress
- **COMPLETED**: Text extraction finished
- **FAILED**: Text extraction failed

#### IndexingJob Model
```python
class IndexingJob(BaseModel):
    id: UUID
    collection_id: Optional[UUID]
    document_id: UUID              # Internal document ID
    created_at: datetime
    updated_at: datetime
    status: IndexingJobStatus      # Overall job status
    indexing_stage: IndexingStage  # Current processing stage
    error: Optional[str]           # Error message if failed
```

### 4. Text Extraction Pipeline

#### DocumentIndexingService
The core service orchestrates the text extraction process:

1. **Document Retrieval**: Fetches document metadata and content
2. **Temporary File Processing**: Uses `TemporaryFileProcessor` for safe file handling
3. **Text Extraction**: Delegates to specialized extractors based on content type
4. **Chunk Creation**: Creates hierarchical text chunks as separate Document entities
5. **Status Management**: Updates indexing status throughout the process

#### TemporaryFileProcessor
A context manager that safely handles document content during processing:
- Fetches content from storage or inline sources
- Creates temporary files with proper extensions
- Ensures cleanup after processing
- Handles both external storage and inlined content

#### Text Extraction Implementation
Currently uses LangChain and PyMuPDF4LLM for PDF processing:
```python
def _extract_text(self, filename: str) -> Generator[LangchainDocument, None, None]:
    loader = PyMuPDF4LLMLoader(filename, mode="page")
    documents = loader.lazy_load()
    for page in documents:
        yield page
```

Each extracted page becomes a separate `TEXT_CHUNK` document with:
- Parent relationship to the original document
- Page-specific metadata (page number, total pages)
- Inlined text content
- Proper content hashing

### 5. Repository Layer

#### Document Repository
Implements the Repository pattern with Protocol-based interfaces:
- **create_document**: Creates new document entries
- **get_document_by_id/external_id**: Retrieval by different ID types
- **list_documents_by_collection_id**: Collection-based queries
- **get_documents_by_root_id**: Hierarchical queries
- **set_indexing_status**: Status management

#### Indexing Job Repository
Manages indexing job persistence:
- **create_indexing_job**: Creates new jobs
- **update_indexing_job**: Status updates with optimistic locking
- **get_jobs_by_status**: Status-based queries
- **get_active_indexing_jobs**: Active job monitoring

#### Transaction Management
Uses SQLAlchemy's nested transactions for consistency:
```python
try:
    with self.db.begin_nested():
        # Database operations
        self.db.flush()
        self.db.refresh(model)
except SQLAlchemyError as e:
    # Automatic rollback
    raise
```

## Document Processing Flow

### Upload Flow
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Service
    participant Storage
    participant Repository
    participant Background
    
    Client->>API: POST /documents (file, collection_id)
    API->>Service: upload_document(file, collection_id, external_id)
    Service->>Storage: store_content(file_stream)
    Storage-->>Service: ContentMetadata(hash, size)
    Service->>Repository: create_document(document)
    Repository-->>Service: Document(with internal ID)
    
    alt autoindex=true
        API->>Repository: create_indexing_job(job)
        API->>Background: schedule_indexing_task(job_id)
    end
    
    API-->>Client: DocumentResponse(external_id)
```

### Indexing Flow
```mermaid
sequenceDiagram
    participant Background
    participant IndexService
    participant Repository
    participant Storage
    participant TextExtractor
    
    Background->>IndexService: index_document(document_id, job_id)
    IndexService->>Repository: get_document_by_id(document_id)
    IndexService->>Repository: set_indexing_status(EXTRACTING)
    
    IndexService->>Storage: get_content(content_hash)
    Storage-->>IndexService: BinaryIO stream
    
    IndexService->>TextExtractor: extract_text(temp_file)
    loop for each page/chunk
        TextExtractor-->>IndexService: LangchainDocument
        IndexService->>Repository: create_document(chunk_doc)
    end
    
    IndexService->>Repository: set_indexing_status(COMPLETED)
```

## Key Design Patterns

### 1. Domain-Driven Design
- Clear separation between domain models and persistence models
- Protocol-based repository interfaces for testability
- Rich domain models with behavior and validation

### 2. Content-Addressed Storage
- Deduplication through content hashing
- Immutable content references
- Atomic storage operations

### 3. Hierarchical Document Model
- Root documents as entry points
- Text chunks as processing artifacts
- Parent-child relationships for navigation

### 4. Background Job Processing
- Asynchronous indexing pipeline
- State tracking and error handling
- Status monitoring and recovery

### 5. Onion Architecture
- Core domain models at the center
- Repository interfaces as boundaries
- Infrastructure concerns at the edges

## Current Limitations

### 1. Synchronous Processing
- Text extraction runs synchronously in background tasks
- No proper job queue or worker system
- Limited concurrency and scalability

### 2. Simple Text Extraction
- Only PDF support via PyMuPDF
- No support for other document formats (Word, PowerPoint, etc.)
- No OCR capabilities for scanned documents

### 3. No Vector Search
- Text chunks stored but not indexed for semantic search
- No embedding generation or vector storage
- Placeholder search implementation

### 4. Basic Error Handling
- Limited retry mechanisms
- No dead letter queues for failed jobs
- Minimal observability and monitoring

## Future Improvements

### 1. Asynchronous Job Queue
- Implement Celery or similar distributed task queue
- Add proper worker management and scaling
- Implement job prioritization and scheduling

### 2. Enhanced Text Extraction
- Support for multiple document formats
- OCR integration for scanned documents
- Better handling of complex document structures

### 3. Vector Search Integration
- Implement embedding generation pipeline
- Add vector database (Pinecone, Weaviate, or local alternatives)
- Semantic search capabilities

### 4. Monitoring and Observability
- Job execution metrics and monitoring
- Error tracking and alerting
- Performance profiling and optimization

### 5. Advanced Content Processing
- Document structure analysis
- Table and image extraction
- Multi-modal content support

## Database Schema

The system uses SQLite with the following key tables:

### documents
- `id` (UUID, PK): Internal document ID
- `external_document_id` (UUID): Public API ID
- `filename`, `content_type`, `size`: File metadata
- `hash`: Content hash for storage reference
- `collection_id` (FK): Associated collection
- `root_id`, `parent_id` (FK): Hierarchical relationships
- `node_type`: Document type (DOCUMENT, TEXT_CHUNK, FACT)
- `inlined_content`: Whether content is stored inline
- `content`: Inlined content (for chunks/facts)
- `metadata`: JSON metadata field
- `indexing_stage`, `indexing_job_id`: Processing status

### indexing_jobs
- `id` (UUID, PK): Job identifier
- `document_id` (FK): Target document
- `collection_id` (FK): Associated collection
- `status`: Job status (PENDING, RUNNING, COMPLETED, FAILED)
- `indexing_stage`: Processing stage
- `error`: Error message if failed
- `created_at`, `updated_at`: Timestamps

This architecture provides a solid foundation for document ingestion with room for significant enhancements in processing capabilities, scalability, and search functionality.
