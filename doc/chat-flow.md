# Chat Flow Documentation

## Overview

The Finchat Service implements a reactive Spring Boot application with MongoDB persistence for handling chat conversations. The system supports streaming chat responses using Server-Sent Events (SSE) and includes mock AI service capabilities.

## Technology Stack

- **Framework**: Spring Boot 3.1.3 with WebFlux (reactive)
- **Database**: MongoDB with Spring Data
- **Security**: OAuth2 authentication
- **Streaming**: Server-Sent Events (SSE)
- **Language**: Java 17

## API Endpoints Data Flow

### GET /conversations

**Endpoint**: `listConversationsConversationsGet()`  
**Location**: `service/src/main/java/de/vctrade/finchat/controller/ConversationsApiControllerImpl.java:75`

**Data Flow**:
1. Request arrives with optional `correlation_id` query parameter
2. Controller calls `ConversationRepository` methods:
   - With correlation_id: `conversationRepository.fetchByReferenceId(correlationId)`
   - Without correlation_id: `conversationRepository.fetchAll()`
3. Results wrapped in `ConversationsResponse` object
4. HTTP 200 response with conversation list or HTTP 404 if error

**Security**: No explicit security annotations - publicly accessible

### POST /conversations

**Endpoint**: `createConversationConversationsCreatePost()`  
**Location**: `service/src/main/java/de/vctrade/finchat/controller/ConversationsApiControllerImpl.java:41`

**Data Flow**:
1. `CreateConversationRequest` received in request body containing:
   - `correlationId` (UUID)
   - Optional `sourceIds` (List<UUID>)
   - Optional `summary` (String)
2. Request directly passed to `conversationRepository.save(createConversationRequest)`
3. Repository creates new `ConversationEntity` in MongoDB
4. Returns `ConversationResponse` with HTTP 201 or HTTP 500 on error

**Security**: No explicit security annotations - publicly accessible

### POST /conversations/{id}/chat

**Endpoint**: `addChatMessageConversationsConversationIdChatPost()`  
**Location**: `service/src/main/java/de/vctrade/finchat/controller/ConversationsApiControllerImpl.java:92`

**Data Flow**:
1. **Authentication Required**: `@PreAuthorize("isAuthenticated() and !isAnonymous()")`
2. Extract authenticated user (`Caller`) from `SecurityContextHolder`
3. Save user message to database:
   - `chatMessageRepository.save(caller.getId(), conversationId, chatRequest.getMessage())`
   - Executed on `Schedulers.boundedElastic()` for blocking I/O
4. Map to `MessageAggregate` and call `aiCommunicationManager.chatWithAI()`
5. Returns `Flux<String>` for SSE streaming response

**Security**: OAuth2 authenticated users only

## Streaming Chat Response Implementation

### Core Implementation

**Service**: `AICommunicationManagerImpl`  
**Location**: `service/src/main/java/de/vctrade/finchat/service/ai/chat/AICommunicationManagerImpl.java:34`

### Stream Flow Architecture

```
User Message → Database Save → AI Communication → Stream Response
     ↓                ↓              ↓                ↓
Authenticated    ChatMessage    Context Provider   SSE Events
   Caller        Repository     Chat History       JSON Chunks
```

### Detailed Stream Process

1. **Context Assembly**: `ChatContextProvider.provide(message)` gathers:
   - Previous conversation history
   - External document context
   - User preferences and settings

2. **Event Stream Creation**:
   ```java
   // START Event
   ContentResponseChunk.builder()
       .type(ResponseChunkType.START)
       .messageId(message.getId())
       .build()
   
   // CONTENT Events (streamed)
   aiCommunicator.streamAiResponse(message, chatContext)
       .map(chunkContent -> ContentResponseChunk.builder()
           .messageId(message.getId())
           .type(ResponseChunkType.CONTENT)
           .content(chunkContent)
           .build())
   
   // END Event (after accumulation)
   ContentResponseChunk.builder()
       .type(ResponseChunkType.END)
       .messageId(message.getId())
       .content(fullContent)
       .build()
   ```

3. **Response Accumulation**: Content chunks are collected using:
   ```java
   contentEventsFlux
       .map(ContentResponseChunk::getContent)
       .reduce(new StringBuilder(), StringBuilder::append)
       .map(StringBuilder::toString)
   ```

4. **Persistence**: Full AI response saved as assistant message in `ResponseChunkType.END` handler

5. **JSON Serialization**: All chunks serialized to JSON strings using Jackson `ObjectMapper`

### Mock AI Service Implementation

**Service**: `AIEmulatorImpl`  
**Location**: `service/src/main/java/de/vctrade/finchat/service/ai/chat/AIEmulatorImpl.java:21`

**Mock Behavior**:
- Predefined response chunks: `["Hi everyone ! ", "I am an AI emulator. ", ...]`
- 150ms delay between chunks: `delayElements(Duration.ofMillis(150))`
- Simple string concatenation for full response assembly

### SSE Event Format

Each SSE event contains a JSON-serialized `ContentResponseChunk`:

```json
{
  "type": "START|CONTENT|END",
  "messageId": "uuid",
  "content": "chunk content or full response"
}
```

**Event Types**:
- `START`: Signals beginning of AI response
- `CONTENT`: Individual content chunks as they arrive
- `END`: Final event with complete response content

### Error Handling

- JSON serialization errors return: `{"type":"error", "message":"Serialization error"}`
- Database save operations wrapped in try-catch with error logging
- Authentication failures return HTTP 401/403
- Missing conversations return HTTP 404

### Security Features

- OAuth2 authentication required for chat endpoints
- User context preserved throughout request lifecycle
- Automatic security filtering in repository layer via `addFilterByAuthentication()`
- Conversation access controlled by authenticated user

### Performance Considerations

- Non-blocking reactive programming with WebFlux
- Blocking database operations executed on `boundedElastic` scheduler
- Stream processing with backpressure handling
- Efficient memory usage through streaming vs. buffering entire responses