<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>de.vctrade</groupId>
    <artifactId>finchat-service</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>finchat-service</name>
    <description>finchat-service</description>
    <modules>
        <module>domain</module>
        <module>persistence</module>
        <module>persistence-api</module>
        <module>service</module>
        <module>service-api</module>
        <module>service-runtime</module>
        <module>document-service</module>
        <module>document-service-api</module>
    </modules>
    <properties>
        <java.version>17</java.version>
        <javax.validation.version>2.0.1.Final</javax.validation.version>
        <spring.version>3.1.3</spring.version>
        <org.apache.commons.version>3.12.0</org.apache.commons.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <org.projectlombok.version>1.18.28</org.projectlombok.version>
        <spring-data-mongodb.version>4.1.3</spring-data-mongodb.version>
        <org.springdoc.versions>2.0.0</org.springdoc.versions>
        <org.springframework.versions>6.1.3</org.springframework.versions>
        <com.google.code.gson.versions>2.9.0</com.google.code.gson.versions>
        <testcontainers.version>1.17.6</testcontainers.version>
        <uuid.creator.version>5.2.0</uuid.creator.version>
        <org.jetbrains.version>17.0.0</org.jetbrains.version>
        <maven.compiler.plugin.version>3.8.1</maven.compiler.plugin.version>
        <maven.war.plugin.version>3.4.0</maven.war.plugin.version>
        <maven.jar.plugin.version>3.3.0</maven.jar.plugin.version>
        <maven.surefire.plugin.version>3.2.2</maven.surefire.plugin.version>
        <maven-deploy-plugin>3.1.1</maven-deploy-plugin>
        <tika.version>3.1.0</tika.version>
        <vctrade.base.version>3.5.0-SNAPSHOT</vctrade.base.version>
        <micrometer-tracing.version>1.1.4</micrometer-tracing.version>
        <jgiven.version>1.3.0</jgiven.version>
        <revision>1.0.0</revision>
        <changelist>-SNAPSHOT</changelist>
        <sha1/>
        <version.plugin.scm>2.0.1</version.plugin.scm>
        <version.plugin.flatten>1.0.18</version.plugin.flatten>
        <kotlin.version>1.9.25</kotlin.version>
        <logback.encoder.version>7.4</logback.encoder.version>
    </properties>
    <profiles>
        <profile>
            <!-- With `-Drelease`, remove -SNAPSHOT -->
            <id>release</id>
            <activation>
                <property>
                    <name>release</name>
                </property>
            </activation>
            <properties>
                <changelist />
            </properties>
        </profile>
        <profile>
            <!-- If `-DcommitHash=SHA1` is given, add hash after version -->
            <id>commit-id</id>
            <activation>
                <property>
                    <name>commitHash</name>
                </property>
            </activation>
            <properties>
                <sha1>-${commitHash}</sha1>
            </properties>
        </profile>
    </profiles>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-bom</artifactId>
                <version>${kotlin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.vctrade</groupId>
                <artifactId>base-bom</artifactId>
                <version>${vctrade.base.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId>
                <version>${tika.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-parsers-standard-package</artifactId>
                <version>${tika.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>mongodb-starter</artifactId>
            <version>${vctrade.base.version}</version>
            <type>test-jar</type>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>base-api</artifactId>
            <version>${vctrade.base.version}</version>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>base</artifactId>
            <version>${vctrade.base.version}</version>
        </dependency>
        <dependency>
            <groupId>de.vctrade</groupId>
            <artifactId>mongodb-starter</artifactId>
            <version>${vctrade.base.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${org.mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing</artifactId>
            <version>${micrometer-tracing.version}</version>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-tracing-bridge-brave</artifactId>
            <version>${micrometer-tracing.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>io.zipkin.aws</groupId>
                    <artifactId>brave-propagation-aws</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logback.encoder.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>Apache Maven Repo</id>
            <url>https://repo1.maven.org/maven2/</url>
            <releases/>
        </repository>
        <repository>
            <id>vcreleases</id>
            <name>vc trade Releases</name>
            <url>https://dev.value-concepts.de:8443/nexus/content/repositories/releases/</url>
        </repository>
        <repository>
            <id>vcsnapshots</id>
            <name>vc trade Snapshots</name>
            <url>https://dev.value-concepts.de:8443/nexus/content/repositories/snapshots/</url>
        </repository>
    </repositories>
    <distributionManagement>
        <site>
            <id>tools.value-concepts.de</id>
            <url>http://www.value-concepts.de</url>
        </site>
        <repository>
            <id>vcreleases</id>
            <name>VCTrade Releases</name>
            <url>https://dev.value-concepts.de:8443/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>vcsnapshots</id>
            <name>VCTrade Snapshots</name>
            <url>https://dev.value-concepts.de:8443/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <scm>
        <connection>scm:git:git://dev.vc-trade.de:~/git/authorization-service.git</connection>
        <developerConnection>scm:git:ssh://dev.vc-trade.de:~/git/authorization-service.git</developerConnection>
        <url>https://dev.value-concepts/git/authorization-service.git</url>
        <tag>HEAD</tag>
    </scm>
    <build>
        <plugins>
            <plugin>
                <groupId>com.outbrain.swinfra</groupId>
                <artifactId>ci-friendly-flatten-maven-plugin</artifactId>
                <version>${version.plugin.flatten}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>clean</goal>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>kotlin-maven-plugin</artifactId>
                <groupId>org.jetbrains.kotlin</groupId>
                <version>${kotlin.version}</version>
                <dependencies>
                    <!-- other dependencies -->
                    <dependency>
                        <groupId>org.mapstruct</groupId>
                        <artifactId>mapstruct-processor</artifactId>
                        <version>${org.mapstruct.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <jvmTarget>${java.version}</jvmTarget>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <jvmTarget>${java.version}</jvmTarget>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>kapt</id>
                        <goals>
                            <goal>kapt</goal>
                        </goals>
                        <configuration>
                            <jvmTarget>${java.version}</jvmTarget>
                            <sourceDirs>
                                <sourceDir>src/main/kotlin</sourceDir>
                            </sourceDirs>
                            <annotationProcessorPaths>
                                <annotationProcessorPath>
                                    <groupId>org.mapstruct</groupId>
                                    <artifactId>mapstruct-processor</artifactId>
                                    <version>${org.mapstruct.version}</version>
                                </annotationProcessorPath>
                            </annotationProcessorPaths>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven.compiler.plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <source>${java.version}</source> <!-- depending on your project -->
                    <target>${java.version}</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${org.projectlombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>